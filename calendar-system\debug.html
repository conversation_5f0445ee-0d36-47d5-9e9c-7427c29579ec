<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>日历系统调试</title>
  <link rel="stylesheet" href="./style.css">
</head>
<body>
  <div id="app">
    <div class="calendar-container">
      <div class="calendar-header">
        <button type="button" onclick="prevYear()">上一年</button>
        <button type="button" onclick="prevMonth()">上一月</button>
        <h2 id="current-date">2025年7月</h2>
        <button type="button" onclick="nextMonth()">下一月</button>
        <button type="button" onclick="nextYear()">下一年</button>
      </div>
      <div class="calendar-grid">
        <div class="weekday-header">
          <div>日</div>
          <div>一</div>
          <div>二</div>
          <div>三</div>
          <div>四</div>
          <div>五</div>
          <div>六</div>
        </div>
        <div id="calendar-body">
          <!-- 日历内容将通过JavaScript生成 -->
        </div>
      </div>
      <div class="calendar-footer">
        <button type="button" onclick="printCalendar()">打印日历</button>
        <button type="button" onclick="exportToPdf()">导出PDF</button>
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentYear = new Date().getFullYear();
    let currentMonth = new Date().getMonth() + 1;
    let holidays = {};

    // 节假日数据
    const holidayData = {
      "2025": {
        "01": {
          "01": "元旦",
          "29": "春节",
          "30": "春节"
        },
        "02": {
          "01": "春节",
          "02": "春节",
          "03": "春节"
        },
        "04": {
          "04": "清明节"
        },
        "05": {
          "01": "劳动节"
        },
        "10": {
          "01": "国庆节",
          "02": "国庆节",
          "03": "国庆节",
          "04": "国庆节",
          "05": "国庆节"
        }
      }
    };

    // 初始化
    function init() {
      updateHolidays();
      renderCalendar();
    }

    // 更新节假日数据
    function updateHolidays() {
      const yearData = holidayData[currentYear] || {};
      const monthData = yearData[String(currentMonth).padStart(2, '0')] || {};
      
      holidays = {};
      for (const [day, name] of Object.entries(monthData)) {
        const dateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${day}`;
        holidays[dateStr] = name;
      }
    }

    // 生成月历矩阵
    function generateMonthMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      
      let matrix = [];
      let week = [];
      
      // 补齐月初空白
      for (let i = 0; i < firstDay.getDay(); i++) {
        week.push(null);
      }
      // 填充日期
      for (let day = 1; day <= daysInMonth; day++) {
        week.push(day);
        if (week.length === 7) {
          matrix.push(week);
          week = [];
        }
      }
      // 补齐月末空白
      if (week.length > 0) {
        while (week.length < 7) {
          week.push(null);
        }
        matrix.push(week);
      }
      return matrix;
    }

    // 渲染日历
    function renderCalendar() {
      // 更新标题
      document.getElementById('current-date').textContent = `${currentYear}年${currentMonth}月`;
      
      // 生成日历内容
      const matrix = generateMonthMatrix(currentYear, currentMonth);
      const calendarBody = document.getElementById('calendar-body');
      calendarBody.innerHTML = '';
      
      matrix.forEach((week, weekIndex) => {
        const weekRow = document.createElement('div');
        weekRow.className = 'week-row';
        
        week.forEach((day, dayIndex) => {
          const dayCell = document.createElement('div');
          dayCell.className = 'day-cell';
          
          if (!day) {
            dayCell.classList.add('empty');
          } else {
            if (isWeekend(weekIndex, dayIndex)) {
              dayCell.classList.add('weekend');
            }
            if (isHoliday(day)) {
              dayCell.classList.add('holiday');
            }
            
            dayCell.innerHTML = `
              <div class="day-number">${day}</div>
              <div class="lunar-day">${getLunarDay(currentYear, currentMonth, day)}</div>
              ${isHoliday(day) ? `<div class="holiday-tag">${getHolidayName(day)}</div>` : ''}
            `;
          }
          
          weekRow.appendChild(dayCell);
        });
        
        calendarBody.appendChild(weekRow);
      });
    }

    // 判断是否为周末
    function isWeekend(weekIndex, dayIndex) {
      return dayIndex === 0 || dayIndex === 6;
    }

    // 判断是否为节假日
    function isHoliday(day) {
      const dateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return holidays[dateStr] !== undefined;
    }

    // 获取节假日名称
    function getHolidayName(day) {
      const dateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return holidays[dateStr] || '';
    }

    // 获取农历日期（简化版本）
    function getLunarDay(year, month, day) {
      const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', 
                          '七月', '八月', '九月', '十月', '冬月', '腊月'];
      const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                        '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                        '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
      
      const dayOfYear = Math.floor((new Date(year, month - 1, day) - new Date(year, 0, 1)) / (1000 * 60 * 60 * 24));
      const lunarMonth = Math.floor(dayOfYear / 30) % 12;
      const lunarDay = (dayOfYear % 30);
      
      return `${lunarMonths[lunarMonth]}${lunarDays[lunarDay]}`;
    }

    // 导航函数
    function prevYear() {
      currentYear--;
      updateHolidays();
      renderCalendar();
    }

    function prevMonth() {
      if (currentMonth === 1) {
        currentMonth = 12;
        currentYear--;
      } else {
        currentMonth--;
      }
      updateHolidays();
      renderCalendar();
    }

    function nextMonth() {
      if (currentMonth === 12) {
        currentMonth = 1;
        currentYear++;
      } else {
        currentMonth++;
      }
      updateHolidays();
      renderCalendar();
    }

    function nextYear() {
      currentYear++;
      updateHolidays();
      renderCalendar();
    }

    // 打印功能
    function printCalendar() {
      window.print();
    }

    function exportToPdf() {
      alert('请使用浏览器的打印功能，选择"保存为PDF"');
      window.print();
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>
