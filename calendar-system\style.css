/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  padding: 20px;
}

/* 日历容器样式 */
.calendar-container {
  max-width: 900px;
  margin: 0 auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 日历头部样式 */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.calendar-header button {
  padding: 6px 12px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.calendar-header button:hover {
  background-color: #e6e6e6;
}

/* 日历网格样式 */
.calendar-grid {
  width: 100%;
  border-collapse: collapse;
}

.weekday-header {
  display: flex;
  background-color: #f9f9f9;
  border-bottom: 1px solid #ddd;
}

.weekday-header div {
  flex: 1;
  padding: 10px;
  text-align: center;
  font-weight: bold;
  color: #333;
}

.week-row {
  display: flex;
  border-bottom: 1px solid #ddd;
}

.day-cell {
  flex: 1;
  min-height: 100px;
  padding: 10px;
  position: relative;
  vertical-align: top;
}

.empty {
  background-color: #f9f9f9;
}

.weekend {
  background-color: #f0f7ff;
}

.holiday {
  background-color: #fff0f0;
}

.day-number {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.lunar-day {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.holiday-tag {
  position: absolute;
  bottom: 5px;
  left: 5px;
  right: 5px;
  font-size: 12px;
  color: #d9534f;
  text-align: center;
}

/* 日历底部样式 */
.calendar-footer {
  display: flex;
  justify-content: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
}

.calendar-footer button {
  margin: 0 10px;
  padding: 8px 16px;
  background-color: #428bca;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.calendar-footer button:hover {
  background-color: #3071a9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .day-cell {
    min-height: 80px;
  }

  .day-number {
    font-size: 16px;
  }

  .lunar-day {
    font-size: 10px;
  }

  .holiday-tag {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .day-cell {
    min-height: 60px;
    padding: 5px;
  }

  .day-number {
    font-size: 14px;
  }

  .lunar-day {
    font-size: 8px;
  }

  .holiday-tag {
    font-size: 8px;
  }
}

/* 打印样式优化 */
@media print {
  @page {
    size: A4;
    margin: 15mm;
  }

  body {
    padding: 0;
  }

  .calendar-container {
    border: none;
    box-shadow: none;
  }

  .calendar-header, .calendar-footer {
    display: none;
  }

  .calendar-grid {
    width: 210mm; /* A4标准宽度 */
    min-height: 297mm;
  }

  .day-cell {
    break-inside: avoid-page;
    border: 0.5pt solid #ddd;
  }
}