<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简单测试</title>
  <style>
    .day-cell { border: 1px solid #ccc; padding: 10px; margin: 2px; display: inline-block; width: 80px; height: 80px; }
    .week-row { display: block; }
  </style>
</head>
<body>
  <h1>日历测试</h1>
  <h2 id="current-date">加载中...</h2>
  <div id="calendar-body">加载中...</div>

  <script>
    console.log('脚本开始执行');
    
    let currentYear = 2025;
    let currentMonth = 7;
    
    function generateMonthMatrix(year, month) {
      console.log('生成矩阵:', year, month);
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      
      console.log('第一天是星期:', firstDay.getDay());
      console.log('本月天数:', daysInMonth);
      
      let matrix = [];
      let week = [];
      
      // 补齐月初空白
      for (let i = 0; i < firstDay.getDay(); i++) {
        week.push(null);
      }
      // 填充日期
      for (let day = 1; day <= daysInMonth; day++) {
        week.push(day);
        if (week.length === 7) {
          matrix.push(week);
          week = [];
        }
      }
      // 补齐月末空白
      if (week.length > 0) {
        while (week.length < 7) {
          week.push(null);
        }
        matrix.push(week);
      }
      
      console.log('生成的矩阵:', matrix);
      return matrix;
    }
    
    function renderCalendar() {
      console.log('开始渲染');
      
      // 更新标题
      const titleElement = document.getElementById('current-date');
      titleElement.textContent = `${currentYear}年${currentMonth}月`;
      
      // 生成日历内容
      const matrix = generateMonthMatrix(currentYear, currentMonth);
      const calendarBody = document.getElementById('calendar-body');
      calendarBody.innerHTML = '';
      
      matrix.forEach((week, weekIndex) => {
        const weekRow = document.createElement('div');
        weekRow.className = 'week-row';
        
        week.forEach((day, dayIndex) => {
          const dayCell = document.createElement('div');
          dayCell.className = 'day-cell';
          
          if (!day) {
            dayCell.textContent = '';
            dayCell.style.backgroundColor = '#f0f0f0';
          } else {
            dayCell.textContent = day;
            dayCell.style.backgroundColor = '#fff';
          }
          
          weekRow.appendChild(dayCell);
        });
        
        calendarBody.appendChild(weekRow);
      });
      
      console.log('渲染完成');
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM加载完成');
      renderCalendar();
    });
    
    console.log('脚本执行完成');
  </script>
</body>
</html>
