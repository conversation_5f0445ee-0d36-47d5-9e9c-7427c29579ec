/*! Vue.js v3.3.4 | (c) 2014-2023 Evan You | MIT License */
var Vue=function(e){"use strict";function t(e,t){const n=Object.create(null),o=e.split(",");for(let e=0;e<o.length;e++)n[o[e]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const n=Object.freeze({}),o=Object.freeze([]),r=()=>{},s=()=>!1,i=/^on[^a-z]/,c=e=>i.test(e),l=e=>e.startsWith("onUpdate:"),a=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},p=Object.prototype.hasOwnProperty,d=(e,t)=>p.call(e,t),f=Array.isArray,h=e=>"[object Map]"===S(e),m=e=>"[object Set]"===S(e),g=e=>"[object Date]"===S(e),v=e=>"function"==typeof e,y=e=>"string"==typeof e,b=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,w=e=>_(e)&&v(e.then)&&v(e.catch),x=Object.prototype.toString,S=e=>x.call(e),C=e=>S(e).slice(8,-1),k=e=>"[object Object]"===S(e),$=e=>y(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,T=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),E=t("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),N=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},O=/-(\\w)/g,A=N((e=>e.replace(O,((e,t)=>t?t.toUpperCase():"")))),R=/\\B([A-Z])/g,P=N((e=>e.replace(R,"-$1").toLowerCase())),I=N((e=>e.charAt(0).toUpperCase()+e.slice(1))),M=N((e=>e?`on${I(e)}`:"")),F=(e,t)=>!Object.is(e,t),j=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},V=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},L=e=>{const t=parseFloat(e);return isNaN(t)?e:t},B=e=>{const t=y(e)?Number(e):NaN;return isNaN(t)?e:t};let U;const D=()=>U||(U="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});
// ... (truncated for brevity - this is a minified Vue.js file)
return e}({});
