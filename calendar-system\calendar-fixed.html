<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>日历系统 - 修复版</title>
  <link rel="stylesheet" href="./style.css">
</head>
<body>
  <div id="app">
    <div class="calendar-container">
      <div class="calendar-header">
        <button type="button" onclick="changeYear(-1)">上一年</button>
        <button type="button" onclick="changeMonth(-1)">上一月</button>
        <h2 id="current-date">加载中...</h2>
        <button type="button" onclick="changeMonth(1)">下一月</button>
        <button type="button" onclick="changeYear(1)">下一年</button>
      </div>
      <div class="calendar-grid">
        <div class="weekday-header">
          <div>日</div>
          <div>一</div>
          <div>二</div>
          <div>三</div>
          <div>四</div>
          <div>五</div>
          <div>六</div>
        </div>
        <div id="calendar-body">
          <!-- 日历内容将通过JavaScript生成 -->
        </div>
      </div>
      <div class="calendar-footer">
        <button type="button" onclick="window.print()">打印日历</button>
        <button type="button" onclick="exportPDF()">导出PDF</button>
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentYear = new Date().getFullYear();
    let currentMonth = new Date().getMonth() + 1;
    
    // 节假日数据
    const holidays = {
      "2025-01-01": "元旦",
      "2025-01-29": "春节",
      "2025-01-30": "春节",
      "2025-02-01": "春节",
      "2025-02-02": "春节",
      "2025-02-03": "春节",
      "2025-04-04": "清明节",
      "2025-05-01": "劳动节",
      "2025-10-01": "国庆节",
      "2025-10-02": "国庆节",
      "2025-10-03": "国庆节",
      "2025-10-04": "国庆节",
      "2025-10-05": "国庆节"
    };
    
    // 农历月份和日期名称
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
    
    // 生成月历矩阵
    function generateCalendarMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      const startDay = firstDay.getDay(); // 0=Sunday, 1=Monday, etc.
      
      const matrix = [];
      let currentWeek = [];
      
      // 添加月初的空白天
      for (let i = 0; i < startDay; i++) {
        currentWeek.push(null);
      }
      
      // 添加月份中的所有天
      for (let day = 1; day <= daysInMonth; day++) {
        currentWeek.push(day);
        
        // 如果一周满了，开始新的一周
        if (currentWeek.length === 7) {
          matrix.push(currentWeek);
          currentWeek = [];
        }
      }
      
      // 添加月末的空白天
      if (currentWeek.length > 0) {
        while (currentWeek.length < 7) {
          currentWeek.push(null);
        }
        matrix.push(currentWeek);
      }
      
      return matrix;
    }
    
    // 获取农历日期（简化版）
    function getLunarDate(year, month, day) {
      const dayOfYear = Math.floor((new Date(year, month - 1, day) - new Date(year, 0, 1)) / (1000 * 60 * 60 * 24));
      const lunarMonth = Math.floor(dayOfYear / 30) % 12;
      const lunarDay = (dayOfYear % 30);
      return `${lunarMonths[lunarMonth]}${lunarDays[lunarDay]}`;
    }
    
    // 检查是否为节假日
    function isHoliday(year, month, day) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return holidays[dateStr];
    }
    
    // 检查是否为周末
    function isWeekend(dayIndex) {
      return dayIndex === 0 || dayIndex === 6; // Sunday or Saturday
    }
    
    // 渲染日历
    function renderCalendar() {
      // 更新标题
      document.getElementById('current-date').textContent = `${currentYear}年${currentMonth}月`;
      
      // 生成日历矩阵
      const matrix = generateCalendarMatrix(currentYear, currentMonth);
      
      // 获取日历主体容器
      const calendarBody = document.getElementById('calendar-body');
      calendarBody.innerHTML = '';
      
      // 渲染每一周
      matrix.forEach(week => {
        const weekRow = document.createElement('div');
        weekRow.className = 'week-row';
        
        // 渲染每一天
        week.forEach((day, dayIndex) => {
          const dayCell = document.createElement('div');
          dayCell.className = 'day-cell';
          
          if (day === null) {
            // 空白天
            dayCell.classList.add('empty');
          } else {
            // 有效天
            const holidayName = isHoliday(currentYear, currentMonth, day);
            const isWeekendDay = isWeekend(dayIndex);
            
            if (isWeekendDay) {
              dayCell.classList.add('weekend');
            }
            
            if (holidayName) {
              dayCell.classList.add('holiday');
            }
            
            // 创建日期内容
            dayCell.innerHTML = `
              <div class="day-number">${day}</div>
              <div class="lunar-day">${getLunarDate(currentYear, currentMonth, day)}</div>
              ${holidayName ? `<div class="holiday-tag">${holidayName}</div>` : ''}
            `;
          }
          
          weekRow.appendChild(dayCell);
        });
        
        calendarBody.appendChild(weekRow);
      });
    }
    
    // 改变年份
    function changeYear(delta) {
      currentYear += delta;
      renderCalendar();
    }
    
    // 改变月份
    function changeMonth(delta) {
      currentMonth += delta;
      
      if (currentMonth > 12) {
        currentMonth = 1;
        currentYear++;
      } else if (currentMonth < 1) {
        currentMonth = 12;
        currentYear--;
      }
      
      renderCalendar();
    }
    
    // 导出PDF
    function exportPDF() {
      alert('请使用浏览器的打印功能，选择"保存为PDF"');
      window.print();
    }
    
    // 初始化
    function init() {
      console.log('初始化日历系统');
      renderCalendar();
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
    } else {
      init();
    }
  </script>
</body>
</html>
