// 全局变量
let currentYear = new Date().getFullYear();
let currentMonth = new Date().getMonth() + 1;

// 节假日数据
const holidays = {
  "2025-01-01": "元旦",
  "2025-01-29": "春节",
  "2025-01-30": "春节",
  "2025-02-01": "春节",
  "2025-02-02": "春节",
  "2025-02-03": "春节",
  "2025-04-04": "清明节",
  "2025-05-01": "劳动节",
  "2025-10-01": "国庆节",
  "2025-10-02": "国庆节",
  "2025-10-03": "国庆节",
  "2025-10-04": "国庆节",
  "2025-10-05": "国庆节"
};

// 农历月份和日期名称
const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                  '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                  '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];

// 生成月历矩阵算法
function generateMonthMatrix(year, month) {
  const firstDay = new Date(year, month - 1, 1);
  const daysInMonth = new Date(year, month, 0).getDate();
  
  let matrix = [];
  let week = [];
  
  // 补齐月初空白
  for (let i = 0; i < firstDay.getDay(); i++) {
    week.push(null);
  }
  // 填充日期
  for (let day = 1; day <= daysInMonth; day++) {
    week.push(day);
    if (week.length === 7) {
      matrix.push(week);
      week = [];
    }
  }
  // 补齐月末空白
  if (week.length > 0) {
    while (week.length < 7) {
      week.push(null);
    }
    matrix.push(week);
  }
  return matrix;
}

// 导航函数
function prevYear() {
  currentYear--;
  renderCalendar();
}

function prevMonth() {
  if (currentMonth === 1) {
    currentMonth = 12;
    currentYear--;
  } else {
    currentMonth--;
  }
  renderCalendar();
}

function nextMonth() {
  if (currentMonth === 12) {
    currentMonth = 1;
    currentYear++;
  } else {
    currentMonth++;
  }
  renderCalendar();
}

function nextYear() {
  currentYear++;
  renderCalendar();
}

// 判断是否为周末
function isWeekend(weekIndex, dayIndex) {
  return dayIndex === 0 || dayIndex === 6;
}

// 判断是否为节假日
function isHoliday(day) {
  const dateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  return holidays[dateStr] !== undefined;
}

// 获取节假日名称
function getHolidayName(day) {
  const dateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  return holidays[dateStr] || '';
}

// 获取农历日期（简化版本）
function getLunarDay(year, month, day) {
  const dayOfYear = Math.floor((new Date(year, month - 1, day) - new Date(year, 0, 1)) / (1000 * 60 * 60 * 24));
  const lunarMonth = Math.floor(dayOfYear / 30) % 12;
  const lunarDay = (dayOfYear % 30);
  return `${lunarMonths[lunarMonth]}${lunarDays[lunarDay]}`;
}

// 渲染日历
function renderCalendar() {
  console.log('开始渲染日历');
  
  // 更新标题
  const titleElement = document.getElementById('current-date');
  if (titleElement) {
    titleElement.textContent = `${currentYear}年${currentMonth}月`;
  }
  
  // 生成日历内容
  const matrix = generateMonthMatrix(currentYear, currentMonth);
  const calendarBody = document.getElementById('calendar-body');
  
  if (!calendarBody) {
    console.error('找不到日历主体元素');
    return;
  }
  
  calendarBody.innerHTML = '';
  
  matrix.forEach((week, weekIndex) => {
    const weekRow = document.createElement('div');
    weekRow.className = 'week-row';
    
    week.forEach((day, dayIndex) => {
      const dayCell = document.createElement('div');
      dayCell.className = 'day-cell';
      
      if (!day) {
        dayCell.classList.add('empty');
      } else {
        if (isWeekend(weekIndex, dayIndex)) {
          dayCell.classList.add('weekend');
        }
        if (isHoliday(day)) {
          dayCell.classList.add('holiday');
        }
        
        dayCell.innerHTML = `
          <div class="day-number">${day}</div>
          <div class="lunar-day">${getLunarDay(currentYear, currentMonth, day)}</div>
          ${isHoliday(day) ? `<div class="holiday-tag">${getHolidayName(day)}</div>` : ''}
        `;
      }
      
      weekRow.appendChild(dayCell);
    });
    
    calendarBody.appendChild(weekRow);
  });
}

// 打印功能
function printCalendar() {
  window.print();
}

function exportToPdf() {
  alert('请使用浏览器的打印功能，选择"保存为PDF"');
  window.print();
}

// 初始化
function init() {
  console.log('初始化日历系统');
  renderCalendar();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);
