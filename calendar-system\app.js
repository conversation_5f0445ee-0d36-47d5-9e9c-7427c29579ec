// 创建Vue应用
const app = Vue.createApp({
  data() {
    return {
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1, // 1-12
      holidays: {},
      holidayData: {}
    };
  },
  mounted() {
    // 加载节假日数据
    this.loadHolidayData();
  },
  computed: {
    // 生成月历矩阵
    monthMatrix() {
      return this.generateMonthMatrix(this.currentYear, this.currentMonth);
    }
  },
  methods: {
    // 加载节假日数据
    loadHolidayData() {
      fetch('./holiday-data.json')
        .then(response => response.json())
        .then(data => {
          this.holidayData = data;
          // 初始化当前年份的节假日
          this.updateHolidays();
        })
        .catch(error => {
          console.error('加载节假日数据错误:', error);
        });
    },
    // 更新节假日数据
    updateHolidays() {
      const yearData = this.holidayData[this.currentYear] || {};
      const monthData = yearData[String(this.currentMonth).padStart(2, '0')] || {};
      
      // 清空当前节假日
      this.holidays = {};
      
      // 填充当前月份的节假日
      for (const [day, name] of Object.entries(monthData)) {
        const dateStr = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${day}`;
        this.holidays[dateStr] = name;
      }
    },
    // 生成月历矩阵算法
    generateMonthMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      
      let matrix = [];
      let week = [];
      
      // 补齐月初空白
      for (let i = 0; i < firstDay.getDay(); i++) {
        week.push(null);
      }
      // 填充日期
      for (let day = 1; day <= daysInMonth; day++) {
        week.push(day);
        if (week.length === 7) {
          matrix.push(week);
          week = [];
        }
      }
      // 补齐月末空白
      if (week.length > 0) {
        while (week.length < 7) {
          week.push(null);
        }
        matrix.push(week);
      }
      return matrix;
    },
    // 上一年
    prevYear() {
      this.currentYear--;
      this.updateHolidays();
    },
    // 上一月
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentMonth = 12;
        this.currentYear--;
      } else {
        this.currentMonth--;
      }
      this.updateHolidays();
    },
    // 下一月
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentMonth = 1;
        this.currentYear++;
      } else {
        this.currentMonth++;
      }
      this.updateHolidays();
    },
    // 下一年
    nextYear() {
      this.currentYear++;
      this.updateHolidays();
    },
    // 判断是否为周末
    isWeekend(weekIndex, dayIndex) {
      return dayIndex === 0 || dayIndex === 6;
    },
    // 判断是否为节假日
    isHoliday(day) {
      const dateStr = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return this.holidays[dateStr] !== undefined;
    },
    // 获取节假日名称
    getHolidayName(day) {
      const dateStr = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return this.holidays[dateStr] || '';
    },
    // 获取农历日期
    getLunarDay(year, month, day) {
      try {
        // 使用lunar-calendar库转换农历
        const lunar = LunarCalendar.solarToLunar(year, month, day);
        return `${lunar.lunarMonthName}${lunar.lunarDayName}`;
      } catch (error) {
        console.error('农历转换错误:', error);
        return '';
      }
    },
    // 打印日历
    printCalendar() {
      printJS({
        printable: 'app',
        type: 'html',
        css: './style.css',
        header: `${this.currentYear}年${this.currentMonth}月日历`
      });
    },
    // 导出PDF
    exportToPdf() {
      printJS({
        printable: 'app',
        type: 'html',
        css: './style.css',
        header: `${this.currentYear}年${this.currentMonth}月日历`,
        documentTitle: `${this.currentYear}年${this.currentMonth}月日历`,
        outputFormat: 'pdf'
      });
    }
  }
});

// 挂载应用
app.mount('#app');