<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue测试</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
  <div id="app">
    <h1>{{ message }}</h1>
    <p>当前年份: {{ currentYear }}</p>
    <p>当前月份: {{ currentMonth }}</p>
  </div>

  <script>
    const { createApp } = Vue;
    
    createApp({
      data() {
        return {
          message: 'Vue正常工作!',
          currentYear: new Date().getFullYear(),
          currentMonth: new Date().getMonth() + 1
        }
      }
    }).mount('#app');
  </script>
</body>
</html>
