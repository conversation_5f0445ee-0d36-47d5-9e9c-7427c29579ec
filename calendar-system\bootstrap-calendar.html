<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bootstrap年历 - 响应式设计</title>
  
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

  <!-- 第三方日期库 -->
  <!-- Lunar JavaScript - 专业农历库 -->
  <script src="https://cdn.jsdelivr.net/npm/lunar-javascript@1.6.12/lunar.min.js"></script>
  <!-- Day.js - 轻量级日期处理库 -->
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/dayjs.min.js"></script>
  <!-- Day.js 插件 -->
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/weekOfYear.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/dayOfYear.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/isLeapYear.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/customParseFormat.js"></script>
  
  <style>
    /* 自定义样式 */
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
    }
    
    .main-container {
      padding: 100px 0 20px 0;
    }
    
    /* 浮动标题 */
    .floating-title {
      position: fixed;
      top: 30px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      -webkit-backdrop-filter: blur(15px);
      backdrop-filter: blur(15px);
      border-radius: 25px;
      padding: 15px 30px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }

    .floating-title:hover {
      transform: translateX(-50%) translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    }

    .title-text {
      font-size: 1.8rem;
      font-weight: 700;
      background: linear-gradient(45deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      white-space: nowrap;
    }
    
    /* 浮动控制按钮 */
    .floating-controls {
      position: fixed;
      top: 50%;
      right: 30px;
      transform: translateY(-50%);
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    
    .floating-btn {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(45deg, #667eea, #764ba2);
      border: none;
      color: white;
      font-size: 1.2rem;
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .floating-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
      color: white;
    }
    
    .floating-btn:active {
      transform: translateY(-1px);
    }

    /* 天气预报样式 */
    .weather-widget {
      position: fixed;
      top: 100px;
      left: 30px;
      width: 300px;
      background: rgba(255, 255, 255, 0.95);
      -webkit-backdrop-filter: blur(15px);
      backdrop-filter: blur(15px);
      border-radius: 15px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.3);
      z-index: 999;
      display: none;
      transition: all 0.3s ease;
    }

    .weather-widget.show {
      display: block;
      animation: slideInLeft 0.3s ease;
    }

    @keyframes slideInLeft {
      from {
        transform: translateX(-100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    .weather-header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 12px 15px;
      border-radius: 15px 15px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
    }

    .weather-close {
      background: none;
      border: none;
      color: white;
      font-size: 1.2rem;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background 0.2s ease;
    }

    .weather-close:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .weather-content {
      padding: 15px;
      max-height: 400px;
      overflow-y: auto;
    }

    .weather-loading {
      text-align: center;
      color: #666;
      padding: 20px;
    }

    .spin {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .weather-frame {
      width: 100%;
      height: 350px;
      border: none;
      border-radius: 8px;
    }

    /* 月份卡片 */
    .month-card {
      background: rgba(255, 255, 255, 0.95);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      cursor: pointer;
      margin-bottom: 20px;
      height: 280px;
    }
    
    .month-card:hover {
      transform: translateY(-5px) scale(1.02);
      box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    }
    
    .month-header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 10px;
      text-align: center;
      font-weight: 600;
      font-size: 1rem;
    }
    
    .weekday-row {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      font-weight: 600;
      font-size: 0.75rem;
      padding: 5px 0;
    }
    
    .day-cell {
      padding: 2px 1px;
      font-size: 0.7rem;
      text-align: center;
      border-right: 1px solid rgba(255, 255, 255, 0.3);
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      min-height: 35px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      transition: all 0.2s ease;
      overflow: hidden;
    }
    
    .day-cell:last-child {
      border-right: none;
    }
    
    .day-cell.empty {
      background: rgba(248, 249, 250, 0.5);
    }
    
    .day-cell.weekend {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      color: #1976d2;
    }
    
    .day-cell.legal-holiday {
      background: linear-gradient(135deg, #ffebee, #f44336);
      color: #c62828;
      font-weight: bold;
      border: 1px solid #f44336;
    }

    .day-cell.solar-holiday {
      background: linear-gradient(135deg, #e8f5e8, #4caf50);
      color: #2e7d32;
    }

    .day-cell.lunar-holiday {
      background: linear-gradient(135deg, #fff8e1, #ffc107);
      color: #f57c00;
    }

    .day-cell.solar-term {
      background: linear-gradient(135deg, #f3e5f5, #9c27b0);
      color: #6a1b9a;
      font-style: italic;
    }

    .day-cell.working {
      background: linear-gradient(135deg, #fff3e0, #ff9800);
      color: #e65100;
      border: 1px dashed #ff9800;
    }
    
    .day-number {
      font-weight: 700;
      font-size: 0.75rem;
      line-height: 1;
      margin-bottom: 1px;
    }

    .lunar-day {
      font-size: 0.45rem;
      color: #7b68ee;
      line-height: 1;
      margin-bottom: 1px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .holiday-tag {
      font-size: 0.35rem;
      background: rgba(244, 67, 54, 0.9);
      color: white;
      border-radius: 2px;
      padding: 1px 2px;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      margin-top: auto;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
      .floating-controls {
        position: fixed;
        bottom: 20px;
        right: 20px;
        top: auto;
        transform: none;
        flex-direction: row;
        gap: 10px;
      }

      .floating-btn {
        width: 50px;
        height: 50px;
        font-size: 1rem;
      }

      .floating-title {
        top: 20px;
        padding: 12px 20px;
        border-radius: 20px;
      }

      .title-text {
        font-size: 1.4rem;
      }

      .main-container {
        padding: 80px 0 20px 0;
      }

      .month-card {
        height: 250px;
      }

      .day-cell {
        min-height: 20px;
        font-size: 0.6rem;
      }

      .weather-widget {
        left: 10px;
        width: 280px;
        top: 80px;
      }
    }
    
    @media (max-width: 576px) {
      .floating-title {
        top: 15px;
        padding: 10px 15px;
        border-radius: 15px;
      }

      .title-text {
        font-size: 1.2rem;
      }

      .main-container {
        padding: 70px 0 20px 0;
      }

      .month-card {
        height: 220px;
      }

      .day-cell {
        min-height: 18px;
        font-size: 0.55rem;
      }

      .day-number {
        font-size: 0.7rem;
      }

      .lunar-day {
        font-size: 0.45rem;
      }

      .weather-widget {
        left: 5px;
        width: 250px;
        top: 70px;
      }
    }
    
    /* 打印样式 */
    @media print {
      body {
        background: white !important;
      }

      .floating-controls {
        display: none !important;
      }

      .floating-title {
        position: static !important;
        transform: none !important;
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        margin-bottom: 20px !important;
        text-align: center;
        border-radius: 0 !important;
      }

      .main-container {
        padding: 20px 0 !important;
      }

      .month-card {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        break-inside: avoid;
        height: auto;
        margin-bottom: 10px;
      }

      .title-text {
        color: #333 !important;
        background: none !important;
        -webkit-text-fill-color: #333 !important;
        font-size: 1.5rem !important;
      }

      .month-header {
        background: #f0f0f0 !important;
        color: #333 !important;
      }
    }
  </style>
</head>
<body>
  <!-- 浮动标题 -->
  <div class="floating-title">
    <h1 class="title-text" id="calendar-title">2025年（乙巳蛇年）日历</h1>
  </div>

  <div class="container-fluid main-container">
    <!-- 年历网格 -->
    <div class="row justify-content-center">
      <div class="col-lg-10 col-xl-8">
        <div class="row" id="calendar-grid">
          <!-- 12个月的日历将通过JavaScript生成 -->
        </div>
      </div>
    </div>
  </div>
  
  <!-- 浮动控制按钮 -->
  <div class="floating-controls">
    <button type="button" class="floating-btn" onclick="changeYear(-1)" title="上一年">
      <i class="bi bi-chevron-up"></i>
    </button>
    <button type="button" class="floating-btn" onclick="window.print()" title="打印日历">
      <i class="bi bi-printer"></i>
    </button>
    <button type="button" class="floating-btn" onclick="toggleWeather()" title="天气预报" id="weather-btn">
      <i class="bi bi-cloud-sun"></i>
    </button>
    <button type="button" class="floating-btn" onclick="changeYear(1)" title="下一年">
      <i class="bi bi-chevron-down"></i>
    </button>
  </div>

  <!-- 天气预报浮动窗口 -->
  <div class="weather-widget" id="weather-widget">
    <div class="weather-header">
      <span>天气预报</span>
      <button type="button" class="weather-close" onclick="toggleWeather()" title="关闭天气">
        <i class="bi bi-x"></i>
      </button>
    </div>
    <div class="weather-content" id="weather-content">
      <div class="weather-loading">
        <i class="bi bi-arrow-clockwise spin"></i>
        正在获取天气信息...
      </div>
    </div>
  </div>

  <!-- Bootstrap 5 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <script>
    // 初始化Day.js插件
    dayjs.extend(dayjs_plugin_weekOfYear);
    dayjs.extend(dayjs_plugin_dayOfYear);
    dayjs.extend(dayjs_plugin_isLeapYear);
    dayjs.extend(dayjs_plugin_customParseFormat);

    // 全局变量
    let currentYear = new Date().getFullYear();

    // 使用专业库检查是否支持
    let lunarLibraryAvailable = typeof Lunar !== 'undefined';
    let dayjsAvailable = typeof dayjs !== 'undefined';

    console.log('农历库可用:', lunarLibraryAvailable);
    console.log('Day.js可用:', dayjsAvailable);
    
    // 月份名称
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 农历月份和日期名称
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
    
    // 扩展的公历节假日数据
    const solarHolidays = {
      "01-01": "元旦", "02-14": "情人节", "03-08": "妇女节", "03-12": "植树节",
      "03-15": "消费者权益日", "04-01": "愚人节", "04-07": "世界卫生日", "04-22": "世界地球日",
      "04-23": "世界读书日", "05-01": "劳动节", "05-04": "青年节", "05-12": "护士节",
      "06-01": "儿童节", "06-05": "世界环境日", "06-26": "国际禁毒日",
      "07-01": "建党节", "07-11": "世界人口日", "08-01": "建军节", "08-19": "中国医师节",
      "09-10": "教师节", "09-20": "全国爱牙日", "10-01": "国庆节", "10-31": "万圣夜",
      "11-09": "消防日", "12-01": "世界艾滋病日", "12-03": "国际残疾人日",
      "12-24": "平安夜", "12-25": "圣诞节"
    };

    // 扩展的农历节假日数据
    const lunarHolidays = {
      "正月初一": "春节", "正月初二": "春节", "正月初三": "春节",
      "正月十五": "元宵节", "二月初二": "龙抬头", "三月初三": "上巳节",
      "四月初八": "浴佛节", "五月初五": "端午节", "六月初六": "天贶节",
      "六月廿四": "火把节", "七月初七": "七夕节", "七月十五": "中元节",
      "八月十五": "中秋节", "九月初九": "重阳节", "十月初一": "寒衣节",
      "十月十五": "下元节", "腊月初八": "腊八节", "腊月廿三": "小年",
      "腊月廿四": "小年", "腊月三十": "除夕", "腊月廿九": "除夕"
    };

    // 2025年精确的法定节假日（根据国务院办公厅通知）
    const legalHolidays2025 = {
      // 元旦：1月1日放假1天
      "2025-01-01": "元旦",
      // 春节：1月28日至2月3日放假调休，共7天
      "2025-01-28": "春节", "2025-01-29": "春节", "2025-01-30": "春节", "2025-01-31": "春节",
      "2025-02-01": "春节", "2025-02-02": "春节", "2025-02-03": "春节",
      // 清明节：4月4日至6日放假调休，共3天
      "2025-04-04": "清明节", "2025-04-05": "清明节", "2025-04-06": "清明节",
      // 劳动节：5月1日至5日放假调休，共5天
      "2025-05-01": "劳动节", "2025-05-02": "劳动节", "2025-05-03": "劳动节",
      "2025-05-04": "劳动节", "2025-05-05": "劳动节",
      // 端午节：5月31日至6月2日放假调休，共3天
      "2025-05-31": "端午节", "2025-06-01": "端午节", "2025-06-02": "端午节",
      // 中秋节：10月6日放假1天
      "2025-10-06": "中秋节",
      // 国庆节：10月1日至7日放假调休，共7天
      "2025-10-01": "国庆节", "2025-10-02": "国庆节", "2025-10-03": "国庆节",
      "2025-10-04": "国庆节", "2025-10-05": "国庆节", "2025-10-07": "国庆节"
    };

    // 2025年调休工作日
    const workingDays2025 = {
      "2025-01-26": "春节调休", "2025-02-08": "春节调休",
      "2025-04-27": "劳动节调休", "2025-05-10": "劳动节调休",
      "2025-09-28": "国庆节调休", "2025-10-11": "国庆节调休"
    };
    
    // 使用专业库计算干支纪年
    function getGanZhiYear(year) {
      if (lunarLibraryAvailable) {
        try {
          // 使用lunar-javascript库获取精确的干支纪年
          const solar = Lunar.fromYmd(year, 1, 1);
          const lunar = solar.getLunar();
          const yearGanZhi = lunar.getYearInGanZhi();
          const yearShengXiao = lunar.getYearShengXiao();

          return {
            ganZhi: yearGanZhi,
            zodiac: yearShengXiao,
            heavenly: yearGanZhi.charAt(0),
            earthly: yearGanZhi.charAt(1)
          };
        } catch (error) {
          console.warn('农历库计算干支纪年失败，使用备用算法:', error);
        }
      }

      // 备用算法（简化版本）
      const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
      const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
      const zodiacAnimals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

      const baseYear = 1984; // 甲子年
      const yearOffset = year - baseYear;
      const heavenlyIndex = yearOffset % 10;
      const earthlyIndex = yearOffset % 12;

      const heavenly = heavenlyStems[heavenlyIndex < 0 ? heavenlyIndex + 10 : heavenlyIndex];
      const earthly = earthlyBranches[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];
      const zodiac = zodiacAnimals[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];

      return {
        ganZhi: heavenly + earthly,
        zodiac: zodiac,
        heavenly: heavenly,
        earthly: earthly
      };
    }
    
    // 使用专业库进行农历转换
    function getLunarDate(year, month, day) {
      if (lunarLibraryAvailable) {
        try {
          // 使用lunar-javascript库进行精确转换
          const solar = Lunar.fromYmd(year, month, day);
          const lunar = solar.getLunar();

          const lunarMonth = lunar.getMonthInChinese();
          const lunarDay = lunar.getDayInChinese();

          return `${lunarMonth}${lunarDay}`;
        } catch (error) {
          console.warn('农历库转换失败，使用备用算法:', error);
        }
      }

      // 备用算法（简化版本）
      const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月',
                          '七月', '八月', '九月', '十月', '冬月', '腊月'];
      const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                        '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                        '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];

      const date = new Date(year, month - 1, day);
      const baseDate = new Date(year, 0, 1);
      const dayOfYear = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));
      const lunarMonthIndex = Math.floor(dayOfYear / 29.5) % 12;
      const lunarDayIndex = Math.floor(dayOfYear % 29.5);
      const lunarMonth = lunarMonths[lunarMonthIndex];
      const lunarDay = lunarDays[Math.min(lunarDayIndex, 29)];

      return `${lunarMonth}${lunarDay}`;
    }

    // 获取详细的农历信息
    function getDetailedLunarInfo(year, month, day) {
      if (lunarLibraryAvailable) {
        try {
          const solar = Lunar.fromYmd(year, month, day);
          const lunar = solar.getLunar();

          return {
            year: lunar.getYearInChinese(),
            month: lunar.getMonthInChinese(),
            day: lunar.getDayInChinese(),
            ganZhi: lunar.getDayInGanZhi(),
            weekDay: solar.getWeekInChinese(),
            solarTerm: solar.getJieQi() || solar.getQi(),
            festivals: lunar.getFestivals().concat(solar.getFestivals()),
            isLeapMonth: lunar.isLeap()
          };
        } catch (error) {
          console.warn('获取详细农历信息失败:', error);
        }
      }

      return {
        year: getGanZhiYear(year).ganZhi + '年',
        month: getLunarDate(year, month, day).slice(0, 2),
        day: getLunarDate(year, month, day).slice(2),
        ganZhi: '',
        weekDay: '',
        solarTerm: '',
        festivals: [],
        isLeapMonth: false
      };
    }
    
    // 生成月历矩阵
    function generateMonthMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      const startDay = firstDay.getDay();
      const matrix = [];
      let currentWeek = [];
      
      for (let i = 0; i < startDay; i++) {
        currentWeek.push(null);
      }
      
      for (let day = 1; day <= daysInMonth; day++) {
        currentWeek.push(day);
        if (currentWeek.length === 7) {
          matrix.push(currentWeek);
          currentWeek = [];
        }
      }
      
      if (currentWeek.length > 0) {
        while (currentWeek.length < 7) {
          currentWeek.push(null);
        }
        matrix.push(currentWeek);
      }
      
      return matrix;
    }
    
    // 获取节假日信息（使用专业库增强）
    function getHolidayInfo(year, month, day) {
      const fullDateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

      // 1. 检查法定节假日
      if (legalHolidays2025[fullDateStr]) {
        return { name: legalHolidays2025[fullDateStr], type: 'legal' };
      }

      // 2. 检查调休工作日
      if (workingDays2025[fullDateStr]) {
        return { name: workingDays2025[fullDateStr], type: 'working' };
      }

      // 3. 使用专业库检查传统节日
      if (lunarLibraryAvailable) {
        try {
          const solar = Lunar.fromYmd(year, month, day);
          const lunar = solar.getLunar();

          // 获取农历节日
          const lunarFestivals = lunar.getFestivals();
          if (lunarFestivals && lunarFestivals.length > 0) {
            return { name: lunarFestivals[0], type: 'lunar' };
          }

          // 获取公历节日
          const solarFestivals = solar.getFestivals();
          if (solarFestivals && solarFestivals.length > 0) {
            return { name: solarFestivals[0], type: 'solar' };
          }

          // 获取节气
          const jieQi = solar.getJieQi();
          if (jieQi) {
            return { name: jieQi, type: 'solar-term' };
          }

          const qi = solar.getQi();
          if (qi) {
            return { name: qi, type: 'solar-term' };
          }
        } catch (error) {
          console.warn('专业库节假日检查失败:', error);
        }
      }

      // 4. 备用检查：公历节假日
      const solarDateStr = `${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      if (solarHolidays[solarDateStr]) {
        return { name: solarHolidays[solarDateStr], type: 'solar' };
      }

      // 5. 备用检查：农历节假日
      const lunarDate = getLunarDate(year, month, day);
      if (lunarHolidays[lunarDate]) {
        return { name: lunarHolidays[lunarDate], type: 'lunar' };
      }

      return null;
    }

    // 检查是否为特殊工作日（调休）
    function isWorkingDay(year, month, day) {
      const fullDateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return workingDays2025[fullDateStr] !== undefined;
    }

    // 使用Day.js增强日期处理
    function getDateInfo(year, month, day) {
      if (dayjsAvailable) {
        try {
          const date = dayjs(`${year}-${month}-${day}`);
          return {
            weekDay: date.day(), // 0=Sunday, 1=Monday, etc.
            weekOfYear: date.week(),
            dayOfYear: date.dayOfYear(),
            isLeapYear: date.isLeapYear(),
            quarter: date.quarter(),
            formatted: date.format('YYYY-MM-DD dddd')
          };
        } catch (error) {
          console.warn('Day.js日期处理失败:', error);
        }
      }

      // 备用方案
      const date = new Date(year, month - 1, day);
      return {
        weekDay: date.getDay(),
        weekOfYear: Math.ceil(((date - new Date(year, 0, 1)) / 86400000 + 1) / 7),
        dayOfYear: Math.floor((date - new Date(year, 0, 1)) / 86400000) + 1,
        isLeapYear: (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0),
        quarter: Math.ceil(month / 3),
        formatted: date.toLocaleDateString('zh-CN')
      };
    }
    
    // 检查是否为周末
    function isWeekend(dayIndex) {
      return dayIndex === 0 || dayIndex === 6;
    }
    
    // 渲染年历
    function renderCalendar() {
      const ganZhi = getGanZhiYear(currentYear);
      document.getElementById('calendar-title').textContent = 
        `${currentYear}年（${ganZhi.ganZhi}${ganZhi.zodiac}年）日历`;
      
      const calendarGrid = document.getElementById('calendar-grid');
      calendarGrid.innerHTML = '';
      
      for (let month = 1; month <= 12; month++) {
        const monthCol = document.createElement('div');
        monthCol.className = 'col-lg-4 col-md-6 col-sm-12';
        
        const monthCard = document.createElement('div');
        monthCard.className = 'month-card';
        
        // 月份标题
        const monthHeader = document.createElement('div');
        monthHeader.className = 'month-header';
        monthHeader.textContent = `${currentYear}年${monthNames[month - 1]}`;
        monthCard.appendChild(monthHeader);
        
        // 星期标题
        const weekdayRow = document.createElement('div');
        weekdayRow.className = 'weekday-row row g-0';
        ['日', '一', '二', '三', '四', '五', '六'].forEach(day => {
          const dayCol = document.createElement('div');
          dayCol.className = 'col text-center';
          dayCol.style.padding = '5px 0';
          dayCol.textContent = day;
          weekdayRow.appendChild(dayCol);
        });
        monthCard.appendChild(weekdayRow);
        
        // 日期网格
        const matrix = generateMonthMatrix(currentYear, month);
        matrix.forEach(week => {
          const weekRow = document.createElement('div');
          weekRow.className = 'row g-0';
          
          week.forEach((day, dayIndex) => {
            const dayCol = document.createElement('div');
            dayCol.className = 'col day-cell';
            
            if (day === null) {
              dayCol.classList.add('empty');
            } else {
              const holidayInfo = getHolidayInfo(currentYear, month, day);
              const dateInfo = getDateInfo(currentYear, month, day);
              const isWeekendDay = isWeekend(dayIndex);
              const isSpecialWorkingDay = isWorkingDay(currentYear, month, day);

              // 添加样式类
              if (isWeekendDay && !isSpecialWorkingDay) {
                dayCol.classList.add('weekend');
              }

              if (holidayInfo) {
                if (holidayInfo.type === 'solar-term') {
                  dayCol.classList.add('solar-term');
                } else if (holidayInfo.type === 'working') {
                  dayCol.classList.add('working');
                } else {
                  dayCol.classList.add(`${holidayInfo.type}-holiday`);
                }
              }

              // 获取详细农历信息
              const lunarInfo = getDetailedLunarInfo(currentYear, month, day);

              dayCol.innerHTML = `
                <div class="day-number">${day}</div>
                <div class="lunar-day">${getLunarDate(currentYear, month, day)}</div>
                ${holidayInfo ? `<div class="holiday-tag">${holidayInfo.name}</div>` : ''}
              `;

              // 添加详细信息到title属性
              let titleInfo = `${currentYear}年${month}月${day}日\n`;
              titleInfo += `农历：${lunarInfo.year}${lunarInfo.month}${lunarInfo.day}\n`;
              if (lunarInfo.ganZhi) titleInfo += `干支：${lunarInfo.ganZhi}\n`;
              if (lunarInfo.solarTerm) titleInfo += `节气：${lunarInfo.solarTerm}\n`;
              if (holidayInfo) titleInfo += `节日：${holidayInfo.name}\n`;
              if (dateInfo.weekOfYear) titleInfo += `第${dateInfo.weekOfYear}周 第${dateInfo.dayOfYear}天`;

              dayCol.title = titleInfo;
            }
            
            weekRow.appendChild(dayCol);
          });
          
          monthCard.appendChild(weekRow);
        });
        
        monthCol.appendChild(monthCard);
        calendarGrid.appendChild(monthCol);
      }
    }
    
    // 改变年份
    function changeYear(delta) {
      currentYear += delta;
      renderCalendar();
    }

    // 天气预报功能
    let weatherVisible = false;

    function toggleWeather() {
      const weatherWidget = document.getElementById('weather-widget');
      const weatherBtn = document.getElementById('weather-btn');

      if (weatherVisible) {
        weatherWidget.classList.remove('show');
        weatherBtn.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
        weatherVisible = false;
      } else {
        weatherWidget.classList.add('show');
        weatherBtn.style.background = 'linear-gradient(45deg, #4caf50, #45a049)';
        loadWeather();
        weatherVisible = true;
      }
    }

    function loadWeather() {
      const weatherContent = document.getElementById('weather-content');

      // 显示加载状态
      weatherContent.innerHTML = `
        <div class="weather-loading">
          <i class="bi bi-arrow-clockwise spin"></i>
          正在获取天气信息...
        </div>
      `;

      // 创建天气预报iframe
      setTimeout(() => {
        try {
          // 根据IP自动判断天气预报代码
          const weatherHTML = `
            <iframe
              class="weather-frame"
              src="http://i.tianqi.com/?c=code&id=12&color=%23FF0000&icon=1&num=5&site=12"
              frameborder="0"
              scrolling="no"
              allowtransparency="true"
              title="天气预报">
            </iframe>
            <div style="text-align: center; margin-top: 10px; font-size: 0.8rem; color: #666;">
              <p>💡 小贴士：天气数据根据您的IP地址自动获取</p>
              <p>🌡️ 支持5天天气预报显示</p>
            </div>
          `;

          weatherContent.innerHTML = weatherHTML;
        } catch (error) {
          console.error('天气预报加载失败:', error);
          weatherContent.innerHTML = `
            <div style="text-align: center; color: #f44336; padding: 20px;">
              <i class="bi bi-exclamation-triangle"></i>
              <p>天气预报加载失败</p>
              <p style="font-size: 0.8rem; color: #666;">请检查网络连接或稍后重试</p>
            </div>
          `;
        }
      }, 500);
    }

    // 获取用户位置（可选功能）
    function getUserLocation() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          function(position) {
            const lat = position.coords.latitude;
            const lon = position.coords.longitude;
            console.log('用户位置:', { lat, lon });
            // 可以根据经纬度获取更精确的天气信息
          },
          function(error) {
            console.warn('获取位置失败:', error.message);
          }
        );
      } else {
        console.warn('浏览器不支持地理定位');
      }
    }

    // 天气预报API备用方案（如果需要更精确的天气数据）
    function loadWeatherAPI() {
      // 这里可以集成其他天气API，如：
      // - 和风天气 API
      // - OpenWeatherMap API
      // - 中国天气网 API
      // 示例代码结构：
      /*
      fetch('https://api.weather.com/v1/current/conditions')
        .then(response => response.json())
        .then(data => {
          // 处理天气数据
        })
        .catch(error => {
          console.error('天气API调用失败:', error);
        });
      */
    }
    
    // 显示库状态信息
    function showLibraryStatus() {
      const statusInfo = [];

      if (lunarLibraryAvailable) {
        statusInfo.push('✅ 农历库已加载 (lunar-javascript)');
      } else {
        statusInfo.push('❌ 农历库未加载，使用备用算法');
      }

      if (dayjsAvailable) {
        statusInfo.push('✅ 日期库已加载 (Day.js)');
      } else {
        statusInfo.push('❌ 日期库未加载，使用原生Date');
      }

      console.log('📚 第三方库状态:');
      statusInfo.forEach(info => console.log(info));

      // 在页面上显示状态（可选）
      if (window.location.search.includes('debug=true')) {
        const statusDiv = document.createElement('div');
        statusDiv.style.cssText = `
          position: fixed; bottom: 10px; left: 10px;
          background: rgba(0,0,0,0.8); color: white;
          padding: 10px; border-radius: 5px; font-size: 12px;
          z-index: 9999; max-width: 300px;
        `;
        statusDiv.innerHTML = statusInfo.join('<br>');
        document.body.appendChild(statusDiv);
      }
    }

    // 测试第三方库功能
    function testLibraryFunctions() {
      if (lunarLibraryAvailable) {
        try {
          const testDate = Lunar.fromYmd(2025, 1, 29);
          const lunar = testDate.getLunar();
          console.log('🧪 农历库测试:', {
            公历: '2025-01-29',
            农历: lunar.toString(),
            干支年: lunar.getYearInGanZhi(),
            生肖: lunar.getYearShengXiao(),
            节日: lunar.getFestivals()
          });
        } catch (error) {
          console.error('❌ 农历库测试失败:', error);
        }
      }

      if (dayjsAvailable) {
        try {
          const testDate = dayjs('2025-01-29');
          console.log('🧪 Day.js测试:', {
            格式化: testDate.format('YYYY年MM月DD日 dddd'),
            第几周: testDate.week(),
            第几天: testDate.dayOfYear(),
            闰年: testDate.isLeapYear()
          });
        } catch (error) {
          console.error('❌ Day.js测试失败:', error);
        }
      }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      showLibraryStatus();
      testLibraryFunctions();
      renderCalendar();
    });
  </script>
</body>
</html>
