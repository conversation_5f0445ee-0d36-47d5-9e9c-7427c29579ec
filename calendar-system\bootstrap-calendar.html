<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bootstrap年历 - 响应式设计</title>
  
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  
  <style>
    /* 自定义样式 */
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
    }
    
    .main-container {
      padding: 20px 0;
    }
    
    .calendar-title {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 30px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .title-text {
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(45deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    /* 浮动控制按钮 */
    .floating-controls {
      position: fixed;
      top: 50%;
      right: 30px;
      transform: translateY(-50%);
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    
    .floating-btn {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(45deg, #667eea, #764ba2);
      border: none;
      color: white;
      font-size: 1.2rem;
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .floating-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
      color: white;
    }
    
    .floating-btn:active {
      transform: translateY(-1px);
    }
    
    /* 月份卡片 */
    .month-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      cursor: pointer;
      margin-bottom: 20px;
      height: 280px;
    }
    
    .month-card:hover {
      transform: translateY(-5px) scale(1.02);
      box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    }
    
    .month-header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 10px;
      text-align: center;
      font-weight: 600;
      font-size: 1rem;
    }
    
    .weekday-row {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      font-weight: 600;
      font-size: 0.75rem;
      padding: 5px 0;
    }
    
    .day-cell {
      padding: 3px 2px;
      font-size: 0.7rem;
      text-align: center;
      border-right: 1px solid rgba(255, 255, 255, 0.3);
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      min-height: 25px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      transition: all 0.2s ease;
    }
    
    .day-cell:last-child {
      border-right: none;
    }
    
    .day-cell.empty {
      background: rgba(248, 249, 250, 0.5);
    }
    
    .day-cell.weekend {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      color: #1976d2;
    }
    
    .day-cell.legal-holiday {
      background: linear-gradient(135deg, #ffebee, #f44336);
      color: #c62828;
      font-weight: bold;
    }
    
    .day-cell.solar-holiday {
      background: linear-gradient(135deg, #e8f5e8, #4caf50);
      color: #2e7d32;
    }
    
    .day-cell.lunar-holiday {
      background: linear-gradient(135deg, #fff8e1, #ffc107);
      color: #f57c00;
    }
    
    .day-number {
      font-weight: 700;
      font-size: 0.8rem;
      line-height: 1;
    }
    
    .lunar-day {
      font-size: 0.5rem;
      color: #7b68ee;
      margin-top: 1px;
      line-height: 1;
    }
    
    .holiday-tag {
      position: absolute;
      bottom: 1px;
      left: 1px;
      right: 1px;
      font-size: 0.4rem;
      background: rgba(244, 67, 54, 0.8);
      color: white;
      border-radius: 2px;
      padding: 1px;
      line-height: 1;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
      .floating-controls {
        position: fixed;
        bottom: 20px;
        right: 20px;
        top: auto;
        transform: none;
        flex-direction: row;
        gap: 10px;
      }
      
      .floating-btn {
        width: 50px;
        height: 50px;
        font-size: 1rem;
      }
      
      .title-text {
        font-size: 1.8rem;
      }
      
      .month-card {
        height: 250px;
      }
      
      .day-cell {
        min-height: 20px;
        font-size: 0.6rem;
      }
    }
    
    @media (max-width: 576px) {
      .title-text {
        font-size: 1.5rem;
      }
      
      .month-card {
        height: 220px;
      }
      
      .day-cell {
        min-height: 18px;
        font-size: 0.55rem;
      }
      
      .day-number {
        font-size: 0.7rem;
      }
      
      .lunar-day {
        font-size: 0.45rem;
      }
    }
    
    /* 打印样式 */
    @media print {
      body {
        background: white !important;
      }
      
      .floating-controls {
        display: none !important;
      }
      
      .month-card {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        break-inside: avoid;
        height: auto;
        margin-bottom: 10px;
      }
      
      .calendar-title {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
      }
      
      .title-text {
        color: #333 !important;
        background: none !important;
        -webkit-text-fill-color: #333 !important;
      }
      
      .month-header {
        background: #f0f0f0 !important;
        color: #333 !important;
      }
    }
  </style>
</head>
<body>
  <div class="container-fluid main-container">
    <!-- 标题 -->
    <div class="row justify-content-center">
      <div class="col-lg-10 col-xl-8">
        <div class="calendar-title text-center">
          <h1 class="title-text" id="calendar-title">2025年（乙巳蛇年）日历</h1>
        </div>
      </div>
    </div>
    
    <!-- 年历网格 -->
    <div class="row justify-content-center">
      <div class="col-lg-10 col-xl-8">
        <div class="row" id="calendar-grid">
          <!-- 12个月的日历将通过JavaScript生成 -->
        </div>
      </div>
    </div>
  </div>
  
  <!-- 浮动控制按钮 -->
  <div class="floating-controls">
    <button type="button" class="floating-btn" onclick="changeYear(-1)" title="上一年">
      <i class="bi bi-chevron-up"></i>
    </button>
    <button type="button" class="floating-btn" onclick="window.print()" title="打印日历">
      <i class="bi bi-printer"></i>
    </button>
    <button type="button" class="floating-btn" onclick="changeYear(1)" title="下一年">
      <i class="bi bi-chevron-down"></i>
    </button>
  </div>

  <!-- Bootstrap 5 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <script>
    // 全局变量
    let currentYear = new Date().getFullYear();
    
    // 干支纪年相关数据
    const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    const zodiacAnimals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
    
    // 月份名称
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 农历月份和日期名称
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
    
    // 公历节假日数据
    const solarHolidays = {
      "01-01": "元旦", "02-14": "情人节", "03-08": "妇女节", "03-12": "植树节",
      "04-01": "愚人节", "05-01": "劳动节", "05-04": "青年节", "06-01": "儿童节",
      "07-01": "建党节", "08-01": "建军节", "09-10": "教师节", "10-01": "国庆节",
      "12-24": "平安夜", "12-25": "圣诞节"
    };
    
    // 农历节假日数据
    const lunarHolidays = {
      "正月初一": "春节", "正月十五": "元宵节", "二月初二": "龙抬头",
      "五月初五": "端午节", "七月初七": "七夕节", "八月十五": "中秋节",
      "九月初九": "重阳节", "腊月初八": "腊八节", "腊月廿三": "小年", "腊月三十": "除夕"
    };
    
    // 2025年特殊节假日
    const specialHolidays2025 = {
      "2025-01-29": "春节", "2025-01-30": "春节", "2025-01-31": "春节",
      "2025-02-01": "春节", "2025-02-02": "春节", "2025-04-04": "清明节",
      "2025-05-01": "劳动节", "2025-06-02": "端午节", "2025-09-15": "中秋节",
      "2025-10-01": "国庆节", "2025-10-02": "国庆节", "2025-10-03": "国庆节"
    };
    
    // 计算干支纪年
    function getGanZhiYear(year) {
      const baseYear = 1984;
      const yearOffset = year - baseYear;
      const heavenlyIndex = yearOffset % 10;
      const earthlyIndex = yearOffset % 12;
      const heavenly = heavenlyStems[heavenlyIndex < 0 ? heavenlyIndex + 10 : heavenlyIndex];
      const earthly = earthlyBranches[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];
      const zodiac = zodiacAnimals[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];
      return { heavenly, earthly, zodiac, ganZhi: heavenly + earthly };
    }
    
    // 简化的农历转换
    function getLunarDate(year, month, day) {
      const date = new Date(year, month - 1, day);
      const baseDate = new Date(year, 0, 1);
      const dayOfYear = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));
      const lunarMonthIndex = Math.floor(dayOfYear / 29.5) % 12;
      const lunarDayIndex = Math.floor(dayOfYear % 29.5);
      const lunarMonth = lunarMonths[lunarMonthIndex];
      const lunarDay = lunarDays[Math.min(lunarDayIndex, 29)];
      return `${lunarMonth}${lunarDay}`;
    }
    
    // 生成月历矩阵
    function generateMonthMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      const startDay = firstDay.getDay();
      const matrix = [];
      let currentWeek = [];
      
      for (let i = 0; i < startDay; i++) {
        currentWeek.push(null);
      }
      
      for (let day = 1; day <= daysInMonth; day++) {
        currentWeek.push(day);
        if (currentWeek.length === 7) {
          matrix.push(currentWeek);
          currentWeek = [];
        }
      }
      
      if (currentWeek.length > 0) {
        while (currentWeek.length < 7) {
          currentWeek.push(null);
        }
        matrix.push(currentWeek);
      }
      
      return matrix;
    }
    
    // 获取节假日信息
    function getHolidayInfo(year, month, day) {
      const fullDateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      if (specialHolidays2025[fullDateStr]) {
        return { name: specialHolidays2025[fullDateStr], type: 'legal' };
      }
      
      const solarDateStr = `${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      if (solarHolidays[solarDateStr]) {
        return { name: solarHolidays[solarDateStr], type: 'solar' };
      }
      
      const lunarDate = getLunarDate(year, month, day);
      if (lunarHolidays[lunarDate]) {
        return { name: lunarHolidays[lunarDate], type: 'lunar' };
      }
      
      return null;
    }
    
    // 检查是否为周末
    function isWeekend(dayIndex) {
      return dayIndex === 0 || dayIndex === 6;
    }
    
    // 渲染年历
    function renderCalendar() {
      const ganZhi = getGanZhiYear(currentYear);
      document.getElementById('calendar-title').textContent = 
        `${currentYear}年（${ganZhi.ganZhi}${ganZhi.zodiac}年）日历`;
      
      const calendarGrid = document.getElementById('calendar-grid');
      calendarGrid.innerHTML = '';
      
      for (let month = 1; month <= 12; month++) {
        const monthCol = document.createElement('div');
        monthCol.className = 'col-lg-4 col-md-6 col-sm-12';
        
        const monthCard = document.createElement('div');
        monthCard.className = 'month-card';
        
        // 月份标题
        const monthHeader = document.createElement('div');
        monthHeader.className = 'month-header';
        monthHeader.textContent = `${currentYear}年${monthNames[month - 1]}`;
        monthCard.appendChild(monthHeader);
        
        // 星期标题
        const weekdayRow = document.createElement('div');
        weekdayRow.className = 'weekday-row row g-0';
        ['日', '一', '二', '三', '四', '五', '六'].forEach(day => {
          const dayCol = document.createElement('div');
          dayCol.className = 'col text-center';
          dayCol.style.padding = '5px 0';
          dayCol.textContent = day;
          weekdayRow.appendChild(dayCol);
        });
        monthCard.appendChild(weekdayRow);
        
        // 日期网格
        const matrix = generateMonthMatrix(currentYear, month);
        matrix.forEach(week => {
          const weekRow = document.createElement('div');
          weekRow.className = 'row g-0';
          
          week.forEach((day, dayIndex) => {
            const dayCol = document.createElement('div');
            dayCol.className = 'col day-cell';
            
            if (day === null) {
              dayCol.classList.add('empty');
            } else {
              const holidayInfo = getHolidayInfo(currentYear, month, day);
              const isWeekendDay = isWeekend(dayIndex);
              
              if (isWeekendDay) {
                dayCol.classList.add('weekend');
              }
              
              if (holidayInfo) {
                dayCol.classList.add(`${holidayInfo.type}-holiday`);
              }
              
              dayCol.innerHTML = `
                <div class="day-number">${day}</div>
                <div class="lunar-day">${getLunarDate(currentYear, month, day)}</div>
                ${holidayInfo ? `<div class="holiday-tag">${holidayInfo.name}</div>` : ''}
              `;
            }
            
            weekRow.appendChild(dayCol);
          });
          
          monthCard.appendChild(weekRow);
        });
        
        monthCol.appendChild(monthCard);
        calendarGrid.appendChild(monthCol);
      }
    }
    
    // 改变年份
    function changeYear(delta) {
      currentYear += delta;
      renderCalendar();
    }
    
    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      renderCalendar();
    });
  </script>
</body>
</html>
