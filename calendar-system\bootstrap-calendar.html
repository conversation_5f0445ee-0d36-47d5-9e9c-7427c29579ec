<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bootstrap年历 - 响应式设计</title>
  
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

  <!-- 第三方日期库 -->
  <!-- Lunar JavaScript - 专业农历库 -->
  <script src="https://cdn.jsdelivr.net/npm/lunar-javascript@1.6.12/lunar.min.js"></script>
  <!-- Day.js - 轻量级日期处理库 -->
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/dayjs.min.js"></script>
  <!-- Day.js 插件 -->
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/weekOfYear.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/dayOfYear.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/isLeapYear.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/customParseFormat.js"></script>

  <!-- SheetJS - Excel导出库 -->
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  
  <style>
    /* 自定义样式 */
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
    }
    
    .main-container {
      padding: 80px 0 20px 0;
    }
    
    /* 浮动标题 */
    .floating-title {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      -webkit-backdrop-filter: blur(15px);
      backdrop-filter: blur(15px);
      border-radius: 0;
      padding: 20px 30px;
      box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
      text-align: center;
    }

    .floating-title:hover {
      background: rgba(255, 255, 255, 0.98);
      box-shadow: 0 4px 25px rgba(0, 0, 0, 0.15);
    }

    .title-text {
      font-size: 1.8rem;
      font-weight: 700;
      background: linear-gradient(45deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      white-space: nowrap;
    }
    
    /* 浮动控制按钮 */
    .floating-controls {
      position: fixed;
      top: 50%;
      right: 30px;
      transform: translateY(-50%);
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    
    .floating-btn {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(45deg, #667eea, #764ba2);
      border: none;
      color: white;
      font-size: 1.2rem;
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .floating-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
      color: white;
    }
    
    .floating-btn:active {
      transform: translateY(-1px);
    }

    /* 天气预报样式 */
    .weather-widget {
      position: fixed;
      top: 90px;
      left: 30px;
      width: 300px;
      background: rgba(255, 255, 255, 0.95);
      -webkit-backdrop-filter: blur(15px);
      backdrop-filter: blur(15px);
      border-radius: 15px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.3);
      z-index: 999;
      display: none;
      transition: all 0.3s ease;
    }

    .weather-widget.show {
      display: block;
      animation: slideInLeft 0.3s ease;
    }

    @keyframes slideInLeft {
      from {
        transform: translateX(-100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    .weather-header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 12px 15px;
      border-radius: 15px 15px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      cursor: move;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      position: relative;
    }

    .weather-header:hover {
      background: linear-gradient(135deg, #5a6fd8, #6a42a0);
    }

    .weather-header:active {
      cursor: grabbing;
    }

    .weather-header::before {
      content: '⋮⋮';
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      font-size: 1.2rem;
      opacity: 0.7;
      letter-spacing: -2px;
    }

    .weather-close {
      background: none;
      border: none;
      color: white;
      font-size: 1.2rem;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background 0.2s ease;
    }

    .weather-close:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .weather-content {
      padding: 15px;
      max-height: 400px;
      overflow-y: auto;
    }

    .weather-loading {
      text-align: center;
      color: #666;
      padding: 20px;
    }

    .spin {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .weather-frame {
      width: 100%;
      height: 350px;
      border: none;
      border-radius: 8px;
    }

    /* 月份卡片 */
    .month-card {
      background: rgba(255, 255, 255, 0.95);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      cursor: pointer;
      margin-bottom: 20px;
      height: 280px;
    }
    
    .month-card:hover {
      transform: translateY(-5px) scale(1.02);
      box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    }
    
    .month-header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 10px;
      text-align: center;
      font-weight: 600;
      font-size: 1rem;
    }
    
    .weekday-row {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      font-weight: 600;
      font-size: 0.75rem;
      padding: 5px 0;
    }
    
    .day-cell {
      padding: 2px 1px;
      font-size: 0.7rem;
      text-align: center;
      border-right: 1px solid rgba(255, 255, 255, 0.3);
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      min-height: 35px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      transition: all 0.2s ease;
      overflow: hidden;
    }
    
    .day-cell:last-child {
      border-right: none;
    }
    
    .day-cell.empty {
      background: rgba(248, 249, 250, 0.5);
    }
    
    .day-cell.weekend {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      color: #1976d2;
    }
    
    .day-cell.legal-holiday {
      background: linear-gradient(135deg, #ffebee, #f44336);
      color: #c62828;
      font-weight: bold;
      border: 1px solid #f44336;
    }

    .day-cell.solar-holiday {
      background: linear-gradient(135deg, #e8f5e8, #4caf50);
      color: #2e7d32;
    }

    .day-cell.lunar-holiday {
      background: linear-gradient(135deg, #fff8e1, #ffc107);
      color: #f57c00;
    }

    .day-cell.solar-term {
      background: linear-gradient(135deg, #f3e5f5, #9c27b0);
      color: #6a1b9a;
      font-style: italic;
    }

    .day-cell.working {
      background: linear-gradient(135deg, #fff3e0, #ff9800);
      color: #e65100;
      border: 1px dashed #ff9800;
    }
    
    .day-number {
      font-weight: 700;
      font-size: 0.75rem;
      line-height: 1;
      margin-bottom: 1px;
    }

    .lunar-day {
      font-size: 0.45rem;
      color: #7b68ee;
      line-height: 1;
      margin-bottom: 1px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .holiday-tag {
      font-size: 0.35rem;
      background: rgba(244, 67, 54, 0.9);
      color: white;
      border-radius: 2px;
      padding: 1px 2px;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      margin-top: auto;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
      .floating-controls {
        position: fixed;
        bottom: 20px;
        right: 20px;
        top: auto;
        transform: none;
        flex-direction: row;
        gap: 10px;
      }

      .floating-btn {
        width: 50px;
        height: 50px;
        font-size: 1rem;
      }

      .floating-title {
        top: 0;
        padding: 15px 20px;
        border-radius: 0;
      }

      .title-text {
        font-size: 1.4rem;
      }

      .main-container {
        padding: 70px 0 20px 0;
      }

      .month-card {
        height: 250px;
      }

      .day-cell {
        min-height: 20px;
        font-size: 0.6rem;
      }

      .weather-widget {
        left: 10px;
        width: 280px;
        top: 70px;
      }
    }
    
    @media (max-width: 576px) {
      .floating-title {
        top: 0;
        padding: 12px 15px;
        border-radius: 0;
      }

      .title-text {
        font-size: 1.2rem;
      }

      .main-container {
        padding: 60px 0 20px 0;
      }

      .month-card {
        height: 220px;
      }

      .day-cell {
        min-height: 18px;
        font-size: 0.55rem;
      }

      .day-number {
        font-size: 0.7rem;
      }

      .lunar-day {
        font-size: 0.45rem;
      }

      .weather-widget {
        left: 5px;
        width: 250px;
        top: 60px;
      }
    }
    
    /* A4打印样式 - 3×4布局 */
    @media print {
      @page {
        size: A4;
        margin: 15mm;
      }

      body {
        background: white !important;
        font-size: 8px !important;
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 0;
      }

      .floating-controls {
        display: none !important;
      }

      .weather-widget {
        display: none !important;
      }

      .floating-title {
        position: static !important;
        width: 100% !important;
        background: white !important;
        box-shadow: none !important;
        border: none !important;
        border-bottom: 2px solid #333 !important;
        margin-bottom: 15mm !important;
        text-align: center;
        border-radius: 0 !important;
        padding: 10mm 0 !important;
      }

      .title-text {
        color: #333 !important;
        background: none !important;
        -webkit-text-fill-color: #333 !important;
        font-size: 18px !important;
        font-weight: bold !important;
      }

      .main-container {
        padding: 0 !important;
        width: 100% !important;
      }

      .container-fluid {
        padding: 0 !important;
        margin: 0 !important;
        max-width: none !important;
      }

      .row {
        margin: 0 !important;
        width: 100% !important;
      }

      .col-lg-10, .col-xl-8 {
        max-width: 100% !important;
        flex: 0 0 100% !important;
        padding: 0 !important;
      }

      .col-lg-4, .col-md-6, .col-sm-12 {
        max-width: 33.333333% !important;
        flex: 0 0 33.333333% !important;
        padding: 2mm !important;
      }

      .month-card {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #999 !important;
        border-radius: 4px !important;
        break-inside: avoid;
        height: 60mm !important;
        margin-bottom: 0 !important;
        page-break-inside: avoid;
      }

      .month-header {
        background: #f0f0f0 !important;
        color: #333 !important;
        font-size: 10px !important;
        font-weight: bold !important;
        padding: 3mm !important;
        text-align: center;
      }

      .weekday-row {
        background: #f8f8f8 !important;
        color: #666 !important;
        font-size: 6px !important;
        font-weight: bold !important;
      }

      .weekday-row .col {
        padding: 1mm 0 !important;
        text-align: center;
        border-right: 0.5px solid #ddd !important;
      }

      .weekday-row .col:last-child {
        border-right: none !important;
      }

      .day-cell {
        min-height: 8mm !important;
        font-size: 5px !important;
        padding: 0.5mm !important;
        border-right: 0.5px solid #eee !important;
        border-bottom: 0.5px solid #eee !important;
        background: white !important;
      }

      .day-cell:nth-child(7n) {
        border-right: none !important;
      }

      .day-cell.weekend {
        background: #f0f7ff !important;
      }

      .day-cell.legal-holiday {
        background: #ffe4e1 !important;
        border: 0.5px solid #ff6b6b !important;
      }

      .day-cell.solar-holiday {
        background: #f0f8ff !important;
      }

      .day-cell.lunar-holiday {
        background: #fff8dc !important;
      }

      .day-cell.solar-term {
        background: #f3e5f5 !important;
      }

      .day-cell.working {
        background: #fff3e0 !important;
      }

      .day-number {
        font-size: 6px !important;
        font-weight: bold !important;
        color: #333 !important;
      }

      .lunar-day {
        font-size: 4px !important;
        color: #666 !important;
      }

      .holiday-tag {
        font-size: 3px !important;
        background: #d9534f !important;
        color: white !important;
        padding: 0.5px 1px !important;
        border-radius: 1px !important;
      }

      /* 确保3×4布局 */
      #calendar-grid {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr) !important;
        grid-template-rows: repeat(4, 1fr) !important;
        gap: 3mm !important;
        width: 100% !important;
        height: auto !important;
      }

      #calendar-grid > div {
        width: 100% !important;
        max-width: none !important;
        flex: none !important;
      }
    }
  </style>
</head>
<body>
  <!-- 浮动标题 -->
  <div class="floating-title">
    <h1 class="title-text" id="calendar-title">2025年（乙巳蛇年）日历</h1>
  </div>

  <div class="container-fluid main-container">
    <!-- 年历网格 -->
    <div class="row justify-content-center">
      <div class="col-lg-10 col-xl-8">
        <div class="row" id="calendar-grid">
          <!-- 12个月的日历将通过JavaScript生成 -->
        </div>
      </div>
    </div>
  </div>
  
  <!-- 浮动控制按钮 -->
  <div class="floating-controls">
    <button type="button" class="floating-btn" onclick="changeYear(-1)" title="上一年">
      <i class="bi bi-chevron-up"></i>
    </button>
    <button type="button" class="floating-btn" onclick="window.print()" title="打印日历">
      <i class="bi bi-printer"></i>
    </button>
    <button type="button" class="floating-btn" onclick="exportToExcel()" title="导出Excel" id="excel-btn">
      <i class="bi bi-file-earmark-spreadsheet"></i>
    </button>
    <button type="button" class="floating-btn" onclick="toggleWeather()" title="天气预报" id="weather-btn">
      <i class="bi bi-cloud-sun"></i>
    </button>
    <button type="button" class="floating-btn" onclick="changeYear(1)" title="下一年">
      <i class="bi bi-chevron-down"></i>
    </button>
  </div>

  <!-- 天气预报浮动窗口 -->
  <div class="weather-widget" id="weather-widget">
    <div class="weather-header">
      <span>天气预报</span>
      <button type="button" class="weather-close" onclick="toggleWeather()" title="关闭天气">
        <i class="bi bi-x"></i>
      </button>
    </div>
    <div class="weather-content" id="weather-content">
      <div class="weather-loading">
        <i class="bi bi-arrow-clockwise spin"></i>
        正在获取天气信息...
      </div>
    </div>
  </div>

  <!-- Bootstrap 5 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <script>
    // 初始化Day.js插件
    dayjs.extend(dayjs_plugin_weekOfYear);
    dayjs.extend(dayjs_plugin_dayOfYear);
    dayjs.extend(dayjs_plugin_isLeapYear);
    dayjs.extend(dayjs_plugin_customParseFormat);

    // 全局变量
    let currentYear = new Date().getFullYear();

    // 使用专业库检查是否支持
    let lunarLibraryAvailable = typeof Lunar !== 'undefined';
    let dayjsAvailable = typeof dayjs !== 'undefined';
    let xlsxAvailable = typeof XLSX !== 'undefined';

    console.log('农历库可用:', lunarLibraryAvailable);
    console.log('Day.js可用:', dayjsAvailable);
    console.log('Excel库可用:', xlsxAvailable);
    
    // 月份名称
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 农历月份和日期名称
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
    
    // 扩展的公历节假日数据
    const solarHolidays = {
      "01-01": "元旦", "02-14": "情人节", "03-08": "妇女节", "03-12": "植树节",
      "03-15": "消费者权益日", "04-01": "愚人节", "04-07": "世界卫生日", "04-22": "世界地球日",
      "04-23": "世界读书日", "05-01": "劳动节", "05-04": "青年节", "05-12": "护士节",
      "06-01": "儿童节", "06-05": "世界环境日", "06-26": "国际禁毒日",
      "07-01": "建党节", "07-11": "世界人口日", "08-01": "建军节", "08-19": "中国医师节",
      "09-10": "教师节", "09-20": "全国爱牙日", "10-01": "国庆节", "10-31": "万圣夜",
      "11-09": "消防日", "12-01": "世界艾滋病日", "12-03": "国际残疾人日",
      "12-24": "平安夜", "12-25": "圣诞节"
    };

    // 扩展的农历节假日数据
    const lunarHolidays = {
      "正月初一": "春节", "正月初二": "春节", "正月初三": "春节",
      "正月十五": "元宵节", "二月初二": "龙抬头", "三月初三": "上巳节",
      "四月初八": "浴佛节", "五月初五": "端午节", "六月初六": "天贶节",
      "六月廿四": "火把节", "七月初七": "七夕节", "七月十五": "中元节",
      "八月十五": "中秋节", "九月初九": "重阳节", "十月初一": "寒衣节",
      "十月十五": "下元节", "腊月初八": "腊八节", "腊月廿三": "小年",
      "腊月廿四": "小年", "腊月三十": "除夕", "腊月廿九": "除夕"
    };

    // 2025年精确的法定节假日（根据国务院办公厅通知）
    const legalHolidays2025 = {
      // 元旦：1月1日放假1天
      "2025-01-01": "元旦",
      // 春节：1月28日至2月3日放假调休，共7天
      "2025-01-28": "春节", "2025-01-29": "春节", "2025-01-30": "春节", "2025-01-31": "春节",
      "2025-02-01": "春节", "2025-02-02": "春节", "2025-02-03": "春节",
      // 清明节：4月4日至6日放假调休，共3天
      "2025-04-04": "清明节", "2025-04-05": "清明节", "2025-04-06": "清明节",
      // 劳动节：5月1日至5日放假调休，共5天
      "2025-05-01": "劳动节", "2025-05-02": "劳动节", "2025-05-03": "劳动节",
      "2025-05-04": "劳动节", "2025-05-05": "劳动节",
      // 端午节：5月31日至6月2日放假调休，共3天
      "2025-05-31": "端午节", "2025-06-01": "端午节", "2025-06-02": "端午节",
      // 中秋节：10月6日放假1天
      "2025-10-06": "中秋节",
      // 国庆节：10月1日至7日放假调休，共7天
      "2025-10-01": "国庆节", "2025-10-02": "国庆节", "2025-10-03": "国庆节",
      "2025-10-04": "国庆节", "2025-10-05": "国庆节", "2025-10-07": "国庆节"
    };

    // 2025年调休工作日
    const workingDays2025 = {
      "2025-01-26": "春节调休", "2025-02-08": "春节调休",
      "2025-04-27": "劳动节调休", "2025-05-10": "劳动节调休",
      "2025-09-28": "国庆节调休", "2025-10-11": "国庆节调休"
    };
    
    // 使用专业库计算干支纪年
    function getGanZhiYear(year) {
      if (lunarLibraryAvailable) {
        try {
          // 使用lunar-javascript库获取精确的干支纪年
          const solar = Lunar.fromYmd(year, 1, 1);
          const lunar = solar.getLunar();
          const yearGanZhi = lunar.getYearInGanZhi();
          const yearShengXiao = lunar.getYearShengXiao();

          return {
            ganZhi: yearGanZhi,
            zodiac: yearShengXiao,
            heavenly: yearGanZhi.charAt(0),
            earthly: yearGanZhi.charAt(1)
          };
        } catch (error) {
          console.warn('农历库计算干支纪年失败，使用备用算法:', error);
        }
      }

      // 备用算法（简化版本）
      const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
      const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
      const zodiacAnimals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

      const baseYear = 1984; // 甲子年
      const yearOffset = year - baseYear;
      const heavenlyIndex = yearOffset % 10;
      const earthlyIndex = yearOffset % 12;

      const heavenly = heavenlyStems[heavenlyIndex < 0 ? heavenlyIndex + 10 : heavenlyIndex];
      const earthly = earthlyBranches[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];
      const zodiac = zodiacAnimals[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];

      return {
        ganZhi: heavenly + earthly,
        zodiac: zodiac,
        heavenly: heavenly,
        earthly: earthly
      };
    }
    
    // 使用专业库进行农历转换
    function getLunarDate(year, month, day) {
      if (lunarLibraryAvailable) {
        try {
          // 使用lunar-javascript库进行精确转换
          const solar = Lunar.fromYmd(year, month, day);
          const lunar = solar.getLunar();

          const lunarMonth = lunar.getMonthInChinese();
          const lunarDay = lunar.getDayInChinese();

          return `${lunarMonth}${lunarDay}`;
        } catch (error) {
          console.warn('农历库转换失败，使用备用算法:', error);
        }
      }

      // 备用算法（简化版本）
      const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月',
                          '七月', '八月', '九月', '十月', '冬月', '腊月'];
      const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                        '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                        '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];

      const date = new Date(year, month - 1, day);
      const baseDate = new Date(year, 0, 1);
      const dayOfYear = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));
      const lunarMonthIndex = Math.floor(dayOfYear / 29.5) % 12;
      const lunarDayIndex = Math.floor(dayOfYear % 29.5);
      const lunarMonth = lunarMonths[lunarMonthIndex];
      const lunarDay = lunarDays[Math.min(lunarDayIndex, 29)];

      return `${lunarMonth}${lunarDay}`;
    }

    // 获取详细的农历信息
    function getDetailedLunarInfo(year, month, day) {
      if (lunarLibraryAvailable) {
        try {
          const solar = Lunar.fromYmd(year, month, day);
          const lunar = solar.getLunar();

          return {
            year: lunar.getYearInChinese(),
            month: lunar.getMonthInChinese(),
            day: lunar.getDayInChinese(),
            ganZhi: lunar.getDayInGanZhi(),
            weekDay: solar.getWeekInChinese(),
            solarTerm: solar.getJieQi() || solar.getQi(),
            festivals: lunar.getFestivals().concat(solar.getFestivals()),
            isLeapMonth: lunar.isLeap()
          };
        } catch (error) {
          console.warn('获取详细农历信息失败:', error);
        }
      }

      return {
        year: getGanZhiYear(year).ganZhi + '年',
        month: getLunarDate(year, month, day).slice(0, 2),
        day: getLunarDate(year, month, day).slice(2),
        ganZhi: '',
        weekDay: '',
        solarTerm: '',
        festivals: [],
        isLeapMonth: false
      };
    }
    
    // 生成月历矩阵
    function generateMonthMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      const startDay = firstDay.getDay();
      const matrix = [];
      let currentWeek = [];
      
      for (let i = 0; i < startDay; i++) {
        currentWeek.push(null);
      }
      
      for (let day = 1; day <= daysInMonth; day++) {
        currentWeek.push(day);
        if (currentWeek.length === 7) {
          matrix.push(currentWeek);
          currentWeek = [];
        }
      }
      
      if (currentWeek.length > 0) {
        while (currentWeek.length < 7) {
          currentWeek.push(null);
        }
        matrix.push(currentWeek);
      }
      
      return matrix;
    }
    
    // 获取节假日信息（使用专业库增强）
    function getHolidayInfo(year, month, day) {
      const fullDateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

      // 1. 检查法定节假日
      if (legalHolidays2025[fullDateStr]) {
        return { name: legalHolidays2025[fullDateStr], type: 'legal' };
      }

      // 2. 检查调休工作日
      if (workingDays2025[fullDateStr]) {
        return { name: workingDays2025[fullDateStr], type: 'working' };
      }

      // 3. 使用专业库检查传统节日
      if (lunarLibraryAvailable) {
        try {
          const solar = Lunar.fromYmd(year, month, day);
          const lunar = solar.getLunar();

          // 获取农历节日
          const lunarFestivals = lunar.getFestivals();
          if (lunarFestivals && lunarFestivals.length > 0) {
            return { name: lunarFestivals[0], type: 'lunar' };
          }

          // 获取公历节日
          const solarFestivals = solar.getFestivals();
          if (solarFestivals && solarFestivals.length > 0) {
            return { name: solarFestivals[0], type: 'solar' };
          }

          // 获取节气
          const jieQi = solar.getJieQi();
          if (jieQi) {
            return { name: jieQi, type: 'solar-term' };
          }

          const qi = solar.getQi();
          if (qi) {
            return { name: qi, type: 'solar-term' };
          }
        } catch (error) {
          console.warn('专业库节假日检查失败:', error);
        }
      }

      // 4. 备用检查：公历节假日
      const solarDateStr = `${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      if (solarHolidays[solarDateStr]) {
        return { name: solarHolidays[solarDateStr], type: 'solar' };
      }

      // 5. 备用检查：农历节假日
      const lunarDate = getLunarDate(year, month, day);
      if (lunarHolidays[lunarDate]) {
        return { name: lunarHolidays[lunarDate], type: 'lunar' };
      }

      return null;
    }

    // 检查是否为特殊工作日（调休）
    function isWorkingDay(year, month, day) {
      const fullDateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return workingDays2025[fullDateStr] !== undefined;
    }

    // 使用Day.js增强日期处理
    function getDateInfo(year, month, day) {
      if (dayjsAvailable) {
        try {
          const date = dayjs(`${year}-${month}-${day}`);
          return {
            weekDay: date.day(), // 0=Sunday, 1=Monday, etc.
            weekOfYear: date.week(),
            dayOfYear: date.dayOfYear(),
            isLeapYear: date.isLeapYear(),
            quarter: date.quarter(),
            formatted: date.format('YYYY-MM-DD dddd')
          };
        } catch (error) {
          console.warn('Day.js日期处理失败:', error);
        }
      }

      // 备用方案
      const date = new Date(year, month - 1, day);
      return {
        weekDay: date.getDay(),
        weekOfYear: Math.ceil(((date - new Date(year, 0, 1)) / 86400000 + 1) / 7),
        dayOfYear: Math.floor((date - new Date(year, 0, 1)) / 86400000) + 1,
        isLeapYear: (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0),
        quarter: Math.ceil(month / 3),
        formatted: date.toLocaleDateString('zh-CN')
      };
    }
    
    // 检查是否为周末
    function isWeekend(dayIndex) {
      return dayIndex === 0 || dayIndex === 6;
    }
    
    // 渲染年历
    function renderCalendar() {
      const ganZhi = getGanZhiYear(currentYear);
      const titleElement = document.getElementById('calendar-title');

      // 检查是否为打印模式
      if (window.matchMedia && window.matchMedia('print').matches) {
        // 打印时只显示年份
        titleElement.textContent = `${currentYear}年`;
      } else {
        // 屏幕显示完整信息
        titleElement.textContent = `${currentYear}年（${ganZhi.ganZhi}${ganZhi.zodiac}年）日历`;
      }

      const calendarGrid = document.getElementById('calendar-grid');
      calendarGrid.innerHTML = '';

      for (let month = 1; month <= 12; month++) {
        const monthCol = document.createElement('div');
        monthCol.className = 'col-lg-4 col-md-6 col-sm-12';
        
        const monthCard = document.createElement('div');
        monthCard.className = 'month-card';
        
        // 月份标题
        const monthHeader = document.createElement('div');
        monthHeader.className = 'month-header';
        monthHeader.textContent = `${currentYear}年${monthNames[month - 1]}`;
        monthCard.appendChild(monthHeader);
        
        // 星期标题
        const weekdayRow = document.createElement('div');
        weekdayRow.className = 'weekday-row row g-0';
        ['日', '一', '二', '三', '四', '五', '六'].forEach(day => {
          const dayCol = document.createElement('div');
          dayCol.className = 'col text-center';
          dayCol.style.padding = '5px 0';
          dayCol.textContent = day;
          weekdayRow.appendChild(dayCol);
        });
        monthCard.appendChild(weekdayRow);
        
        // 日期网格
        const matrix = generateMonthMatrix(currentYear, month);
        matrix.forEach(week => {
          const weekRow = document.createElement('div');
          weekRow.className = 'row g-0';
          
          week.forEach((day, dayIndex) => {
            const dayCol = document.createElement('div');
            dayCol.className = 'col day-cell';
            
            if (day === null) {
              dayCol.classList.add('empty');
            } else {
              const holidayInfo = getHolidayInfo(currentYear, month, day);
              const dateInfo = getDateInfo(currentYear, month, day);
              const isWeekendDay = isWeekend(dayIndex);
              const isSpecialWorkingDay = isWorkingDay(currentYear, month, day);

              // 添加样式类
              if (isWeekendDay && !isSpecialWorkingDay) {
                dayCol.classList.add('weekend');
              }

              if (holidayInfo) {
                if (holidayInfo.type === 'solar-term') {
                  dayCol.classList.add('solar-term');
                } else if (holidayInfo.type === 'working') {
                  dayCol.classList.add('working');
                } else {
                  dayCol.classList.add(`${holidayInfo.type}-holiday`);
                }
              }

              // 获取详细农历信息
              const lunarInfo = getDetailedLunarInfo(currentYear, month, day);

              dayCol.innerHTML = `
                <div class="day-number">${day}</div>
                <div class="lunar-day">${getLunarDate(currentYear, month, day)}</div>
                ${holidayInfo ? `<div class="holiday-tag">${holidayInfo.name}</div>` : ''}
              `;

              // 添加详细信息到title属性
              let titleInfo = `${currentYear}年${month}月${day}日\n`;
              titleInfo += `农历：${lunarInfo.year}${lunarInfo.month}${lunarInfo.day}\n`;
              if (lunarInfo.ganZhi) titleInfo += `干支：${lunarInfo.ganZhi}\n`;
              if (lunarInfo.solarTerm) titleInfo += `节气：${lunarInfo.solarTerm}\n`;
              if (holidayInfo) titleInfo += `节日：${holidayInfo.name}\n`;
              if (dateInfo.weekOfYear) titleInfo += `第${dateInfo.weekOfYear}周 第${dateInfo.dayOfYear}天`;

              dayCol.title = titleInfo;
            }
            
            weekRow.appendChild(dayCol);
          });
          
          monthCard.appendChild(weekRow);
        });
        
        monthCol.appendChild(monthCard);
        calendarGrid.appendChild(monthCol);
      }
    }
    
    // 改变年份
    function changeYear(delta) {
      currentYear += delta;
      renderCalendar();
    }

    // Excel导出功能
    function exportToExcel() {
      if (!xlsxAvailable) {
        alert('Excel导出库未加载，请刷新页面重试');
        return;
      }

      try {
        // 显示导出状态
        const excelBtn = document.getElementById('excel-btn');
        const originalHTML = excelBtn.innerHTML;
        excelBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
        excelBtn.disabled = true;

        // 创建工作簿
        const workbook = XLSX.utils.book_new();

        // 创建年度概览工作表
        createYearOverviewSheet(workbook);

        // 为每个月创建详细工作表
        for (let month = 1; month <= 12; month++) {
          createMonthDetailSheet(workbook, month);
        }

        // 创建节假日汇总工作表
        createHolidaySummarySheet(workbook);

        // 导出文件
        const ganZhi = getGanZhiYear(currentYear);
        const fileName = `${currentYear}年日历_${ganZhi.ganZhi}${ganZhi.zodiac}年.xlsx`;

        setTimeout(() => {
          XLSX.writeFile(workbook, fileName);

          // 恢复按钮状态
          excelBtn.innerHTML = originalHTML;
          excelBtn.disabled = false;

          // 显示成功提示
          showExportSuccess(fileName);
        }, 500);

      } catch (error) {
        console.error('Excel导出失败:', error);
        alert('Excel导出失败，请稍后重试');

        // 恢复按钮状态
        const excelBtn = document.getElementById('excel-btn');
        excelBtn.innerHTML = '<i class="bi bi-file-earmark-spreadsheet"></i>';
        excelBtn.disabled = false;
      }
    }

    // 创建年度概览工作表
    function createYearOverviewSheet(workbook) {
      const ganZhi = getGanZhiYear(currentYear);
      const data = [];

      // 标题行
      data.push([`${currentYear}年日历概览 (${ganZhi.ganZhi}${ganZhi.zodiac}年)`]);
      data.push([]);

      // 表头
      data.push(['月份', '天数', '工作日', '周末', '法定假日', '传统节日', '二十四节气']);

      // 统计每个月的数据
      for (let month = 1; month <= 12; month++) {
        const monthData = getMonthStatistics(month);
        data.push([
          `${month}月`,
          monthData.totalDays,
          monthData.workingDays,
          monthData.weekends,
          monthData.legalHolidays,
          monthData.traditionalHolidays,
          monthData.solarTerms
        ]);
      }

      // 年度汇总
      data.push([]);
      const yearStats = getYearStatistics();
      data.push(['年度汇总', yearStats.totalDays, yearStats.workingDays, yearStats.weekends,
                yearStats.legalHolidays, yearStats.traditionalHolidays, yearStats.solarTerms]);

      const worksheet = XLSX.utils.aoa_to_sheet(data);

      // 设置列宽
      worksheet['!cols'] = [
        { width: 15 }, { width: 10 }, { width: 10 }, { width: 10 },
        { width: 12 }, { width: 12 }, { width: 12 }
      ];

      XLSX.utils.book_append_sheet(workbook, worksheet, '年度概览');
    }

    // 创建月份详细工作表
    function createMonthDetailSheet(workbook, month) {
      const data = [];
      const monthName = monthNames[month - 1];

      // 标题
      data.push([`${currentYear}年${monthName}详细日历`]);
      data.push([]);

      // 表头
      data.push(['日期', '星期', '农历', '节假日', '类型', '备注']);

      // 获取月份数据
      const daysInMonth = new Date(currentYear, month, 0).getDate();

      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(currentYear, month - 1, day);
        const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
        const weekDay = weekDays[date.getDay()];
        const lunarDate = getLunarDate(currentYear, month, day);
        const holidayInfo = getHolidayInfo(currentYear, month, day);
        const dateInfo = getDateInfo(currentYear, month, day);

        let holidayName = '';
        let holidayType = '';
        let remarks = '';

        if (holidayInfo) {
          holidayName = holidayInfo.name;
          holidayType = getHolidayTypeName(holidayInfo.type);
        }

        if (dateInfo.weekDay === 0 || dateInfo.weekDay === 6) {
          if (!holidayInfo) {
            holidayType = '周末';
          }
        }

        // 添加特殊备注
        if (isWorkingDay(currentYear, month, day)) {
          remarks = '调休工作日';
        }

        data.push([
          `${month}月${day}日`,
          weekDay,
          lunarDate,
          holidayName,
          holidayType,
          remarks
        ]);
      }

      const worksheet = XLSX.utils.aoa_to_sheet(data);

      // 设置列宽
      worksheet['!cols'] = [
        { width: 12 }, { width: 8 }, { width: 15 },
        { width: 15 }, { width: 12 }, { width: 15 }
      ];

      XLSX.utils.book_append_sheet(workbook, worksheet, monthName);
    }

    // 创建节假日汇总工作表
    function createHolidaySummarySheet(workbook) {
      const data = [];

      // 标题
      data.push([`${currentYear}年节假日汇总`]);
      data.push([]);

      // 法定节假日
      data.push(['法定节假日']);
      data.push(['日期', '节日名称', '星期', '农历', '备注']);

      const legalHolidayDates = [];
      for (let month = 1; month <= 12; month++) {
        const daysInMonth = new Date(currentYear, month, 0).getDate();
        for (let day = 1; day <= daysInMonth; day++) {
          const holidayInfo = getHolidayInfo(currentYear, month, day);
          if (holidayInfo && holidayInfo.type === 'legal') {
            const date = new Date(currentYear, month - 1, day);
            const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
            legalHolidayDates.push({
              date: `${month}月${day}日`,
              name: holidayInfo.name,
              weekDay: weekDays[date.getDay()],
              lunar: getLunarDate(currentYear, month, day),
              remark: ''
            });
          }
        }
      }

      legalHolidayDates.forEach(holiday => {
        data.push([holiday.date, holiday.name, holiday.weekDay, holiday.lunar, holiday.remark]);
      });

      // 传统节日
      data.push([]);
      data.push(['传统节日']);
      data.push(['日期', '节日名称', '星期', '农历', '类型']);

      const traditionalHolidays = [];
      for (let month = 1; month <= 12; month++) {
        const daysInMonth = new Date(currentYear, month, 0).getDate();
        for (let day = 1; day <= daysInMonth; day++) {
          const holidayInfo = getHolidayInfo(currentYear, month, day);
          if (holidayInfo && (holidayInfo.type === 'lunar' || holidayInfo.type === 'solar')) {
            const date = new Date(currentYear, month - 1, day);
            const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
            traditionalHolidays.push({
              date: `${month}月${day}日`,
              name: holidayInfo.name,
              weekDay: weekDays[date.getDay()],
              lunar: getLunarDate(currentYear, month, day),
              type: getHolidayTypeName(holidayInfo.type)
            });
          }
        }
      }

      traditionalHolidays.forEach(holiday => {
        data.push([holiday.date, holiday.name, holiday.weekDay, holiday.lunar, holiday.type]);
      });

      // 二十四节气
      data.push([]);
      data.push(['二十四节气']);
      data.push(['日期', '节气名称', '星期', '农历', '说明']);

      const solarTerms = [];
      for (let month = 1; month <= 12; month++) {
        const daysInMonth = new Date(currentYear, month, 0).getDate();
        for (let day = 1; day <= daysInMonth; day++) {
          const holidayInfo = getHolidayInfo(currentYear, month, day);
          if (holidayInfo && holidayInfo.type === 'solar-term') {
            const date = new Date(currentYear, month - 1, day);
            const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
            solarTerms.push({
              date: `${month}月${day}日`,
              name: holidayInfo.name,
              weekDay: weekDays[date.getDay()],
              lunar: getLunarDate(currentYear, month, day),
              description: '二十四节气'
            });
          }
        }
      }

      solarTerms.forEach(term => {
        data.push([term.date, term.name, term.weekDay, term.lunar, term.description]);
      });

      const worksheet = XLSX.utils.aoa_to_sheet(data);

      // 设置列宽
      worksheet['!cols'] = [
        { width: 12 }, { width: 15 }, { width: 8 },
        { width: 15 }, { width: 12 }
      ];

      XLSX.utils.book_append_sheet(workbook, worksheet, '节假日汇总');
    }

    // 获取月份统计数据
    function getMonthStatistics(month) {
      const daysInMonth = new Date(currentYear, month, 0).getDate();
      let workingDays = 0, weekends = 0, legalHolidays = 0, traditionalHolidays = 0, solarTerms = 0;

      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(currentYear, month - 1, day);
        const holidayInfo = getHolidayInfo(currentYear, month, day);
        const isWeekendDay = date.getDay() === 0 || date.getDay() === 6;
        const isSpecialWorkingDay = isWorkingDay(currentYear, month, day);

        if (holidayInfo) {
          if (holidayInfo.type === 'legal') legalHolidays++;
          else if (holidayInfo.type === 'lunar' || holidayInfo.type === 'solar') traditionalHolidays++;
          else if (holidayInfo.type === 'solar-term') solarTerms++;
        }

        if (isWeekendDay && !isSpecialWorkingDay) {
          weekends++;
        } else if (!holidayInfo || holidayInfo.type !== 'legal') {
          workingDays++;
        }
      }

      return {
        totalDays: daysInMonth,
        workingDays,
        weekends,
        legalHolidays,
        traditionalHolidays,
        solarTerms
      };
    }

    // 获取年度统计数据
    function getYearStatistics() {
      let totalDays = 0, workingDays = 0, weekends = 0, legalHolidays = 0, traditionalHolidays = 0, solarTerms = 0;

      for (let month = 1; month <= 12; month++) {
        const monthStats = getMonthStatistics(month);
        totalDays += monthStats.totalDays;
        workingDays += monthStats.workingDays;
        weekends += monthStats.weekends;
        legalHolidays += monthStats.legalHolidays;
        traditionalHolidays += monthStats.traditionalHolidays;
        solarTerms += monthStats.solarTerms;
      }

      return {
        totalDays,
        workingDays,
        weekends,
        legalHolidays,
        traditionalHolidays,
        solarTerms
      };
    }

    // 获取节假日类型名称
    function getHolidayTypeName(type) {
      const typeNames = {
        'legal': '法定假日',
        'solar': '公历节日',
        'lunar': '农历节日',
        'solar-term': '二十四节气',
        'working': '调休工作日'
      };
      return typeNames[type] || '其他';
    }

    // 显示导出成功提示
    function showExportSuccess(fileName) {
      // 创建临时提示元素
      const toast = document.createElement('div');
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(45deg, #4caf50, #45a049);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-size: 14px;
        max-width: 300px;
        animation: slideInRight 0.3s ease;
      `;

      toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <i class="bi bi-check-circle" style="font-size: 18px;"></i>
          <div>
            <div style="font-weight: bold;">导出成功！</div>
            <div style="font-size: 12px; opacity: 0.9; margin-top: 2px;">${fileName}</div>
          </div>
        </div>
      `;

      // 添加动画样式
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
      `;
      document.head.appendChild(style);

      document.body.appendChild(toast);

      // 3秒后自动移除
      setTimeout(() => {
        toast.style.animation = 'slideInRight 0.3s ease reverse';
        setTimeout(() => {
          document.body.removeChild(toast);
          document.head.removeChild(style);
        }, 300);
      }, 3000);
    }

    // 天气预报功能
    let weatherVisible = false;

    // 拖动功能变量
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };
    let weatherWidget = null;

    function toggleWeather() {
      weatherWidget = document.getElementById('weather-widget');
      const weatherBtn = document.getElementById('weather-btn');

      if (weatherVisible) {
        weatherWidget.classList.remove('show');
        weatherBtn.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
        weatherVisible = false;
      } else {
        weatherWidget.classList.add('show');
        weatherBtn.style.background = 'linear-gradient(45deg, #4caf50, #45a049)';
        loadWeather();
        initDragFunctionality();
        weatherVisible = true;
      }
    }

    // 初始化拖动功能
    function initDragFunctionality() {
      const weatherHeader = weatherWidget.querySelector('.weather-header');

      // 鼠标按下事件
      weatherHeader.addEventListener('mousedown', startDrag);

      // 双击重置位置
      weatherHeader.addEventListener('dblclick', resetWeatherPosition);

      // 全局鼠标事件
      document.addEventListener('mousemove', drag);
      document.addEventListener('mouseup', stopDrag);

      // 触摸事件支持（移动端）
      weatherHeader.addEventListener('touchstart', startDragTouch, { passive: false });
      document.addEventListener('touchmove', dragTouch, { passive: false });
      document.addEventListener('touchend', stopDrag);
    }

    // 开始拖动（鼠标）
    function startDrag(e) {
      isDragging = true;
      const rect = weatherWidget.getBoundingClientRect();
      dragOffset.x = e.clientX - rect.left;
      dragOffset.y = e.clientY - rect.top;

      weatherWidget.style.transition = 'none';
      document.body.style.cursor = 'grabbing';
      e.preventDefault();
    }

    // 开始拖动（触摸）
    function startDragTouch(e) {
      const touch = e.touches[0];
      isDragging = true;
      const rect = weatherWidget.getBoundingClientRect();
      dragOffset.x = touch.clientX - rect.left;
      dragOffset.y = touch.clientY - rect.top;

      weatherWidget.style.transition = 'none';
      e.preventDefault();
    }

    // 拖动中（鼠标）
    function drag(e) {
      if (!isDragging || !weatherWidget) return;

      const x = e.clientX - dragOffset.x;
      const y = e.clientY - dragOffset.y;

      // 限制拖动范围，防止拖出屏幕
      const maxX = window.innerWidth - weatherWidget.offsetWidth;
      const maxY = window.innerHeight - weatherWidget.offsetHeight;

      const constrainedX = Math.max(0, Math.min(x, maxX));
      const constrainedY = Math.max(0, Math.min(y, maxY));

      weatherWidget.style.left = constrainedX + 'px';
      weatherWidget.style.top = constrainedY + 'px';

      e.preventDefault();
    }

    // 拖动中（触摸）
    function dragTouch(e) {
      if (!isDragging || !weatherWidget) return;

      const touch = e.touches[0];
      const x = touch.clientX - dragOffset.x;
      const y = touch.clientY - dragOffset.y;

      // 限制拖动范围
      const maxX = window.innerWidth - weatherWidget.offsetWidth;
      const maxY = window.innerHeight - weatherWidget.offsetHeight;

      const constrainedX = Math.max(0, Math.min(x, maxX));
      const constrainedY = Math.max(0, Math.min(y, maxY));

      weatherWidget.style.left = constrainedX + 'px';
      weatherWidget.style.top = constrainedY + 'px';

      e.preventDefault();
    }

    // 停止拖动
    function stopDrag() {
      if (isDragging && weatherWidget) {
        isDragging = false;
        weatherWidget.style.transition = 'all 0.3s ease';
        document.body.style.cursor = '';

        // 保存位置到localStorage
        const rect = weatherWidget.getBoundingClientRect();
        localStorage.setItem('weatherWidgetPosition', JSON.stringify({
          x: rect.left,
          y: rect.top
        }));
      }
    }

    // 恢复上次位置
    function restoreWeatherPosition() {
      const savedPosition = localStorage.getItem('weatherWidgetPosition');
      if (savedPosition && weatherWidget) {
        try {
          const position = JSON.parse(savedPosition);

          // 检查位置是否在屏幕范围内
          const maxX = window.innerWidth - weatherWidget.offsetWidth;
          const maxY = window.innerHeight - weatherWidget.offsetHeight;

          if (position.x >= 0 && position.x <= maxX && position.y >= 0 && position.y <= maxY) {
            weatherWidget.style.left = position.x + 'px';
            weatherWidget.style.top = position.y + 'px';
          }
        } catch (error) {
          console.warn('恢复天气窗口位置失败:', error);
        }
      }
    }

    // 重置位置到默认
    function resetWeatherPosition() {
      if (weatherWidget) {
        weatherWidget.style.left = '30px';
        weatherWidget.style.top = '90px';
        localStorage.removeItem('weatherWidgetPosition');
      }
    }

    function loadWeather() {
      const weatherContent = document.getElementById('weather-content');

      // 显示加载状态
      weatherContent.innerHTML = `
        <div class="weather-loading">
          <i class="bi bi-arrow-clockwise spin"></i>
          正在获取天气信息...
        </div>
      `;

      // 创建天气预报iframe
      setTimeout(() => {
        try {
          // 根据IP自动判断天气预报代码
          const weatherHTML = `
            <iframe
              class="weather-frame"
              src="http://i.tianqi.com/?c=code&id=12&color=%23FF0000&icon=1&num=5&site=12"
              frameborder="0"
              scrolling="no"
              allowtransparency="true"
              title="天气预报">
            </iframe>
            <div style="text-align: center; margin-top: 10px; font-size: 0.8rem; color: #666;">
              <p>💡 小贴士：天气数据根据您的IP地址自动获取</p>
              <p>🌡️ 支持5天天气预报显示</p>
              <p>🖱️ 拖动标题栏移动窗口 | 双击重置位置</p>
            </div>
          `;

          weatherContent.innerHTML = weatherHTML;

          // 恢复上次保存的位置
          setTimeout(() => {
            restoreWeatherPosition();
          }, 100);

        } catch (error) {
          console.error('天气预报加载失败:', error);
          weatherContent.innerHTML = `
            <div style="text-align: center; color: #f44336; padding: 20px;">
              <i class="bi bi-exclamation-triangle"></i>
              <p>天气预报加载失败</p>
              <p style="font-size: 0.8rem; color: #666;">请检查网络连接或稍后重试</p>
              <button onclick="resetWeatherPosition()" style="margin-top: 10px; padding: 5px 10px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                重置位置
              </button>
            </div>
          `;
        }
      }, 500);
    }

    // 获取用户位置（可选功能）
    function getUserLocation() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          function(position) {
            const lat = position.coords.latitude;
            const lon = position.coords.longitude;
            console.log('用户位置:', { lat, lon });
            // 可以根据经纬度获取更精确的天气信息
          },
          function(error) {
            console.warn('获取位置失败:', error.message);
          }
        );
      } else {
        console.warn('浏览器不支持地理定位');
      }
    }

    // 天气预报API备用方案（如果需要更精确的天气数据）
    function loadWeatherAPI() {
      // 这里可以集成其他天气API，如：
      // - 和风天气 API
      // - OpenWeatherMap API
      // - 中国天气网 API
      // 示例代码结构：
      /*
      fetch('https://api.weather.com/v1/current/conditions')
        .then(response => response.json())
        .then(data => {
          // 处理天气数据
        })
        .catch(error => {
          console.error('天气API调用失败:', error);
        });
      */
    }
    
    // 显示库状态信息
    function showLibraryStatus() {
      const statusInfo = [];

      if (lunarLibraryAvailable) {
        statusInfo.push('✅ 农历库已加载 (lunar-javascript)');
      } else {
        statusInfo.push('❌ 农历库未加载，使用备用算法');
      }

      if (dayjsAvailable) {
        statusInfo.push('✅ 日期库已加载 (Day.js)');
      } else {
        statusInfo.push('❌ 日期库未加载，使用原生Date');
      }

      if (xlsxAvailable) {
        statusInfo.push('✅ Excel库已加载 (SheetJS)');
      } else {
        statusInfo.push('❌ Excel库未加载，导出功能不可用');
      }

      console.log('📚 第三方库状态:');
      statusInfo.forEach(info => console.log(info));

      // 在页面上显示状态（可选）
      if (window.location.search.includes('debug=true')) {
        const statusDiv = document.createElement('div');
        statusDiv.style.cssText = `
          position: fixed; bottom: 10px; left: 10px;
          background: rgba(0,0,0,0.8); color: white;
          padding: 10px; border-radius: 5px; font-size: 12px;
          z-index: 9999; max-width: 300px;
        `;
        statusDiv.innerHTML = statusInfo.join('<br>');
        document.body.appendChild(statusDiv);
      }
    }

    // 测试第三方库功能
    function testLibraryFunctions() {
      if (lunarLibraryAvailable) {
        try {
          const testDate = Lunar.fromYmd(2025, 1, 29);
          const lunar = testDate.getLunar();
          console.log('🧪 农历库测试:', {
            公历: '2025-01-29',
            农历: lunar.toString(),
            干支年: lunar.getYearInGanZhi(),
            生肖: lunar.getYearShengXiao(),
            节日: lunar.getFestivals()
          });
        } catch (error) {
          console.error('❌ 农历库测试失败:', error);
        }
      }

      if (dayjsAvailable) {
        try {
          const testDate = dayjs('2025-01-29');
          console.log('🧪 Day.js测试:', {
            格式化: testDate.format('YYYY年MM月DD日 dddd'),
            第几周: testDate.week(),
            第几天: testDate.dayOfYear(),
            闰年: testDate.isLeapYear()
          });
        } catch (error) {
          console.error('❌ Day.js测试失败:', error);
        }
      }
    }

    // 窗口大小变化时调整天气窗口位置
    window.addEventListener('resize', function() {
      if (weatherVisible && weatherWidget) {
        const rect = weatherWidget.getBoundingClientRect();
        const maxX = window.innerWidth - weatherWidget.offsetWidth;
        const maxY = window.innerHeight - weatherWidget.offsetHeight;

        // 如果窗口超出屏幕范围，调整位置
        if (rect.left > maxX || rect.top > maxY) {
          const newX = Math.max(0, Math.min(rect.left, maxX));
          const newY = Math.max(0, Math.min(rect.top, maxY));

          weatherWidget.style.left = newX + 'px';
          weatherWidget.style.top = newY + 'px';

          // 保存新位置
          localStorage.setItem('weatherWidgetPosition', JSON.stringify({
            x: newX,
            y: newY
          }));
        }
      }
    });

    // 打印事件监听
    window.addEventListener('beforeprint', function() {
      // 打印前更新标题为简洁版本
      const titleElement = document.getElementById('calendar-title');
      titleElement.textContent = `${currentYear}年`;
    });

    window.addEventListener('afterprint', function() {
      // 打印后恢复完整标题
      const ganZhi = getGanZhiYear(currentYear);
      const titleElement = document.getElementById('calendar-title');
      titleElement.textContent = `${currentYear}年（${ganZhi.ganZhi}${ganZhi.zodiac}年）日历`;
    });

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      showLibraryStatus();
      testLibraryFunctions();
      renderCalendar();
    });
  </script>
</body>
</html>
