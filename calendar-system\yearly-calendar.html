<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>年历 - A4格式</title>
  <style>
    /* A4页面设置 */
    @page {
      size: A4;
      margin: 8mm;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      font-size: 8px;
      line-height: 1.1;
      background: white;
      width: 210mm;
      height: 297mm;
    }
    
    .year-header {
      text-align: center;
      margin-bottom: 8px;
      padding: 5px 0;
    }

    .year-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 3px;
    }

    .year-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      margin-top: 5px;
    }

    .year-controls button {
      padding: 3px 8px;
      background: #f0f0f0;
      border: 1px solid #ccc;
      border-radius: 3px;
      cursor: pointer;
      font-size: 10px;
    }

    .year-controls button:hover {
      background: #e0e0e0;
    }
    
    /* 年历网格布局 - 3列4行 */
    .yearly-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(4, 1fr);
      gap: 4px;
      height: 260mm;
      width: 194mm;
      margin: 0 auto;
    }

    /* 每个月的容器 */
    .month-container {
      border: 0.5px solid #ccc;
      border-radius: 2px;
      overflow: hidden;
      background: white;
      display: flex;
      flex-direction: column;
      height: 62mm;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .month-container:hover {
      border-color: #007bff;
      box-shadow: 0 2px 8px rgba(0,123,255,0.2);
      transform: scale(1.02);
    }

    .month-header {
      background: #f8f9fa;
      padding: 2px 4px;
      text-align: center;
      font-weight: bold;
      font-size: 9px;
      color: #333;
      border-bottom: 0.5px solid #ddd;
      height: 12px;
    }
    
    .weekday-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: #f0f0f0;
      font-size: 6px;
      font-weight: bold;
      height: 8px;
    }

    .weekday-header div {
      padding: 1px;
      text-align: center;
      border-right: 0.5px solid #ddd;
      line-height: 6px;
    }

    .weekday-header div:last-child {
      border-right: none;
    }

    .month-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      flex: 1;
    }

    .day-cell {
      border-right: 0.5px solid #eee;
      border-bottom: 0.5px solid #eee;
      padding: 0.5px;
      font-size: 6px;
      position: relative;
      height: 8mm;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
    
    .day-cell:nth-child(7n) {
      border-right: none;
    }
    
    .day-cell.empty {
      background: #f9f9f9;
    }
    
    .day-cell.weekend {
      background: #f0f7ff;
    }
    
    .day-cell.holiday {
      background: #fff0f0;
    }
    
    .day-number {
      font-weight: bold;
      color: #333;
      font-size: 7px;
      line-height: 1;
    }

    .lunar-day {
      font-size: 4px;
      color: #666;
      margin-top: 0.5px;
      line-height: 1;
    }

    .holiday-tag {
      font-size: 3px;
      color: #d9534f;
      text-align: center;
      position: absolute;
      bottom: 0.5px;
      left: 0;
      right: 0;
      line-height: 1;
    }
    
    /* 打印样式 */
    @media print {
      body {
        font-size: 8px;
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 0;
      }

      .year-header {
        margin-bottom: 5mm;
        padding: 2mm 0;
      }

      .year-title {
        font-size: 16px;
      }

      .year-controls {
        display: none;
      }

      .yearly-grid {
        height: 260mm;
        width: 194mm;
        margin: 0 auto;
        gap: 3mm;
      }

      .month-container {
        break-inside: avoid;
        height: 62mm;
        border: 0.5px solid #999;
      }

      .month-header {
        font-size: 8px;
        height: 10px;
        padding: 1px 2px;
      }

      .weekday-header {
        font-size: 5px;
        height: 6px;
      }

      .weekday-header div {
        line-height: 5px;
      }

      .day-cell {
        height: 7.5mm;
        font-size: 5px;
      }

      .day-number {
        font-size: 6px;
      }

      .lunar-day {
        font-size: 3px;
      }

      .holiday-tag {
        font-size: 2.5px;
      }
    }
    
    /* 单月显示模式 */
    .single-month-mode {
      display: none;
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
    }

    .single-month-mode.active {
      display: block;
    }

    .single-month-container {
      border: 2px solid #007bff;
      border-radius: 8px;
      overflow: hidden;
      background: white;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .single-month-header {
      background: #007bff;
      color: white;
      padding: 15px;
      text-align: center;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .back-button {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .back-button:hover {
      background: rgba(255,255,255,0.3);
    }

    .single-weekday-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: #f8f9fa;
      font-size: 14px;
      font-weight: bold;
    }

    .single-weekday-header div {
      padding: 12px;
      text-align: center;
      border-right: 1px solid #ddd;
    }

    .single-weekday-header div:last-child {
      border-right: none;
    }

    .single-month-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
    }

    .single-day-cell {
      border-right: 1px solid #eee;
      border-bottom: 1px solid #eee;
      padding: 12px 8px;
      font-size: 14px;
      position: relative;
      min-height: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .single-day-cell:nth-child(7n) {
      border-right: none;
    }

    .single-day-cell.empty {
      background: #f9f9f9;
    }

    .single-day-cell.weekend {
      background: #f0f7ff;
    }

    .single-day-cell.holiday {
      background: #fff0f0;
    }

    .single-day-number {
      font-weight: bold;
      color: #333;
      font-size: 18px;
      margin-bottom: 4px;
    }

    .single-lunar-day {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .single-holiday-tag {
      font-size: 10px;
      color: #d9534f;
      text-align: center;
      position: absolute;
      bottom: 4px;
      left: 4px;
      right: 4px;
      background: rgba(217, 83, 79, 0.1);
      padding: 2px;
      border-radius: 2px;
    }

    /* 响应式调整 */
    @media (max-width: 1200px) {
      .yearly-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(6, 1fr);
      }
    }

    @media (max-width: 800px) {
      .yearly-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(12, 1fr);
      }

      .single-month-mode {
        margin: 10px;
        padding: 10px;
      }

      .single-day-cell {
        min-height: 60px;
        padding: 8px 4px;
      }

      .single-day-number {
        font-size: 16px;
      }

      .single-lunar-day {
        font-size: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="year-header">
    <h1 class="year-title" id="year-title">2025年日历</h1>
    <div class="year-controls">
      <button type="button" onclick="changeYear(-1)">上一年</button>
      <button type="button" onclick="window.print()">打印日历</button>
      <button type="button" onclick="changeYear(1)">下一年</button>
    </div>
  </div>
  
  <div class="yearly-grid" id="yearly-grid">
    <!-- 12个月的日历将通过JavaScript生成 -->
  </div>

  <!-- 单月显示模式 -->
  <div class="single-month-mode" id="single-month-mode">
    <div class="single-month-container">
      <div class="single-month-header">
        <button type="button" class="back-button" onclick="showYearlyView()">← 返回年历</button>
        <span id="single-month-title">2025年1月</span>
        <div style="width: 80px;"></div> <!-- 占位符保持居中 -->
      </div>
      <div class="single-weekday-header">
        <div>日</div>
        <div>一</div>
        <div>二</div>
        <div>三</div>
        <div>四</div>
        <div>五</div>
        <div>六</div>
      </div>
      <div class="single-month-grid" id="single-month-grid">
        <!-- 单月日历内容将通过JavaScript生成 -->
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentYear = new Date().getFullYear();
    let currentViewMode = 'yearly'; // 'yearly' 或 'single'
    let selectedMonth = 1;
    
    // 节假日数据
    const holidays = {
      "2025-01-01": "元旦",
      "2025-01-29": "春节",
      "2025-01-30": "春节",
      "2025-02-01": "春节",
      "2025-02-02": "春节",
      "2025-02-03": "春节",
      "2025-04-04": "清明节",
      "2025-05-01": "劳动节",
      "2025-10-01": "国庆节",
      "2025-10-02": "国庆节",
      "2025-10-03": "国庆节",
      "2025-10-04": "国庆节",
      "2025-10-05": "国庆节"
    };
    
    // 月份名称
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 干支纪年相关数据
    const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    const zodiacAnimals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

    // 农历月份和日期名称
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];

    // 农历数字转中文
    const chineseNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
    const chineseDayNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                              '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                              '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
    
    // 生成月历矩阵
    function generateMonthMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      const startDay = firstDay.getDay();
      
      const matrix = [];
      let currentWeek = [];
      
      // 添加月初的空白天
      for (let i = 0; i < startDay; i++) {
        currentWeek.push(null);
      }
      
      // 添加月份中的所有天
      for (let day = 1; day <= daysInMonth; day++) {
        currentWeek.push(day);
        
        if (currentWeek.length === 7) {
          matrix.push(currentWeek);
          currentWeek = [];
        }
      }
      
      // 添加月末的空白天
      if (currentWeek.length > 0) {
        while (currentWeek.length < 7) {
          currentWeek.push(null);
        }
        matrix.push(currentWeek);
      }
      
      return matrix;
    }
    
    // 计算干支纪年
    function getGanZhiYear(year) {
      // 以1984年（甲子年）为基准
      const baseYear = 1984;
      const yearOffset = year - baseYear;

      const heavenlyIndex = yearOffset % 10;
      const earthlyIndex = yearOffset % 12;

      const heavenly = heavenlyStems[heavenlyIndex < 0 ? heavenlyIndex + 10 : heavenlyIndex];
      const earthly = earthlyBranches[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];
      const zodiac = zodiacAnimals[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];

      return { heavenly, earthly, zodiac, ganZhi: heavenly + earthly };
    }

    // 2025年农历数据（部分月份的农历对应关系）
    const lunar2025Data = {
      // 格式：'MM-DD': { month: 农历月, day: 农历日, monthName: '月份名', dayName: '日期名' }
      '01-01': { month: 12, day: 2, monthName: '腊月', dayName: '初二' },
      '01-29': { month: 1, day: 1, monthName: '正月', dayName: '初一' }, // 春节
      '02-12': { month: 1, day: 15, monthName: '正月', dayName: '十五' }, // 元宵节
      '02-28': { month: 2, day: 1, monthName: '二月', dayName: '初一' },
      '03-29': { month: 3, day: 1, monthName: '三月', dayName: '初一' },
      '04-27': { month: 4, day: 1, monthName: '四月', dayName: '初一' },
      '05-27': { month: 5, day: 1, monthName: '五月', dayName: '初一' },
      '06-25': { month: 6, day: 1, monthName: '六月', dayName: '初一' },
      '07-24': { month: 7, day: 1, monthName: '七月', dayName: '初一' },
      '08-23': { month: 8, day: 1, monthName: '八月', dayName: '初一' },
      '09-21': { month: 9, day: 1, monthName: '九月', dayName: '初一' },
      '10-21': { month: 10, day: 1, monthName: '十月', dayName: '初一' },
      '11-19': { month: 11, day: 1, monthName: '冬月', dayName: '初一' },
      '12-19': { month: 12, day: 1, monthName: '腊月', dayName: '初一' }
    };

    // 改进的农历转换（基于实际农历数据）
    function getLunarDate(year, month, day) {
      if (year === 2025) {
        // 使用真实的2025年农历数据
        const dateKey = `${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        if (lunar2025Data[dateKey]) {
          return `${lunar2025Data[dateKey].monthName}${lunar2025Data[dateKey].dayName}`;
        }

        // 对于没有精确数据的日期，进行近似计算
        const date = new Date(year, month - 1, day);

        // 找到最近的农历月初
        let nearestNewMoon = null;
        let minDiff = Infinity;

        for (const [key, value] of Object.entries(lunar2025Data)) {
          if (value.day === 1) { // 农历月初
            const [m, d] = key.split('-').map(Number);
            const lunarNewMoonDate = new Date(year, m - 1, d);
            const diff = Math.abs(date - lunarNewMoonDate);
            if (diff < minDiff) {
              minDiff = diff;
              nearestNewMoon = { date: lunarNewMoonDate, lunar: value, key };
            }
          }
        }

        if (nearestNewMoon) {
          const daysDiff = Math.floor((date - nearestNewMoon.date) / (1000 * 60 * 60 * 24));
          const lunarDay = daysDiff + 1;

          if (lunarDay > 0 && lunarDay <= 30) {
            return `${nearestNewMoon.lunar.monthName}${lunarDays[lunarDay - 1]}`;
          }
        }
      }

      // 对于其他年份或无法计算的日期，使用简化算法
      const date = new Date(year, month - 1, day);
      const baseDate = new Date(year, 0, 1);
      const dayOfYear = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));

      const lunarMonthIndex = Math.floor(dayOfYear / 29.5) % 12;
      const lunarDayIndex = Math.floor(dayOfYear % 29.5);

      const lunarMonth = lunarMonths[lunarMonthIndex];
      const lunarDay = lunarDays[Math.min(lunarDayIndex, 29)];

      return `${lunarMonth}${lunarDay}`;
    }

    // 获取完整的农历信息（包含干支纪年）
    function getFullLunarInfo(year, month, day) {
      const ganZhi = getGanZhiYear(year);
      const lunarDate = getLunarDate(year, month, day);

      return {
        ganZhiYear: ganZhi.ganZhi + '年',
        zodiacYear: ganZhi.zodiac + '年',
        lunarDate: lunarDate,
        fullInfo: `${ganZhi.ganZhi}${ganZhi.zodiac}年 ${lunarDate}`
      };
    }

    // 获取农历日期显示（用于日历格子中）
    function getLunarDateDisplay(year, month, day) {
      return getLunarDate(year, month, day);
    }

    // 获取年份的干支信息（用于标题显示）
    function getYearGanZhiInfo(year) {
      const ganZhi = getGanZhiYear(year);
      return `${ganZhi.ganZhi}${ganZhi.zodiac}年`;
    }
    
    // 检查是否为节假日
    function isHoliday(year, month, day) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return holidays[dateStr];
    }
    
    // 检查是否为周末
    function isWeekend(dayIndex) {
      return dayIndex === 0 || dayIndex === 6;
    }
    
    // 渲染单个月份
    function renderMonth(year, month) {
      const monthContainer = document.createElement('div');
      monthContainer.className = 'month-container';
      monthContainer.onclick = () => showSingleMonth(month);

      // 月份标题
      const monthHeader = document.createElement('div');
      monthHeader.className = 'month-header';
      monthHeader.textContent = `${year}年${monthNames[month - 1]}`;
      monthContainer.appendChild(monthHeader);
      
      // 星期标题
      const weekdayHeader = document.createElement('div');
      weekdayHeader.className = 'weekday-header';
      ['日', '一', '二', '三', '四', '五', '六'].forEach(day => {
        const dayDiv = document.createElement('div');
        dayDiv.textContent = day;
        weekdayHeader.appendChild(dayDiv);
      });
      monthContainer.appendChild(weekdayHeader);
      
      // 月份网格
      const monthGrid = document.createElement('div');
      monthGrid.className = 'month-grid';
      
      const matrix = generateMonthMatrix(year, month);
      
      matrix.forEach(week => {
        week.forEach((day, dayIndex) => {
          const dayCell = document.createElement('div');
          dayCell.className = 'day-cell';
          
          if (day === null) {
            dayCell.classList.add('empty');
          } else {
            const holidayName = isHoliday(year, month, day);
            const isWeekendDay = isWeekend(dayIndex);
            
            if (isWeekendDay) {
              dayCell.classList.add('weekend');
            }
            
            if (holidayName) {
              dayCell.classList.add('holiday');
            }
            
            dayCell.innerHTML = `
              <div class="day-number">${day}</div>
              <div class="lunar-day">${getLunarDateDisplay(year, month, day)}</div>
              ${holidayName ? `<div class="holiday-tag">${holidayName}</div>` : ''}
            `;
          }
          
          monthGrid.appendChild(dayCell);
        });
      });
      
      monthContainer.appendChild(monthGrid);
      return monthContainer;
    }
    
    // 渲染整年日历
    function renderYearlyCalendar() {
      // 更新标题，包含干支纪年
      const ganZhiInfo = getYearGanZhiInfo(currentYear);
      document.getElementById('year-title').textContent = `${currentYear}年（${ganZhiInfo}）日历`;

      // 清空网格
      const yearlyGrid = document.getElementById('yearly-grid');
      yearlyGrid.innerHTML = '';

      // 生成12个月
      for (let month = 1; month <= 12; month++) {
        const monthElement = renderMonth(currentYear, month);
        yearlyGrid.appendChild(monthElement);
      }
    }
    
    // 改变年份
    function changeYear(delta) {
      currentYear += delta;
      if (currentViewMode === 'yearly') {
        renderYearlyCalendar();
      } else {
        renderSingleMonth();
      }
    }

    // 显示单个月份
    function showSingleMonth(month) {
      selectedMonth = month;
      currentViewMode = 'single';

      // 隐藏年历，显示单月
      document.getElementById('yearly-grid').style.display = 'none';
      document.getElementById('single-month-mode').classList.add('active');

      renderSingleMonth();
    }

    // 显示年历视图
    function showYearlyView() {
      currentViewMode = 'yearly';

      // 显示年历，隐藏单月
      document.getElementById('yearly-grid').style.display = 'grid';
      document.getElementById('single-month-mode').classList.remove('active');
    }

    // 渲染单月详细视图
    function renderSingleMonth() {
      // 更新标题，包含干支纪年
      const ganZhiInfo = getYearGanZhiInfo(currentYear);
      document.getElementById('single-month-title').textContent = `${currentYear}年（${ganZhiInfo}）${monthNames[selectedMonth - 1]}`;

      // 生成日历矩阵
      const matrix = generateMonthMatrix(currentYear, selectedMonth);

      // 获取单月网格容器
      const singleMonthGrid = document.getElementById('single-month-grid');
      singleMonthGrid.innerHTML = '';

      // 渲染每一周
      matrix.forEach(week => {
        week.forEach((day, dayIndex) => {
          const dayCell = document.createElement('div');
          dayCell.className = 'single-day-cell';

          if (day === null) {
            // 空白天
            dayCell.classList.add('empty');
          } else {
            // 有效天
            const holidayName = isHoliday(currentYear, selectedMonth, day);
            const isWeekendDay = isWeekend(dayIndex);

            if (isWeekendDay) {
              dayCell.classList.add('weekend');
            }

            if (holidayName) {
              dayCell.classList.add('holiday');
            }

            // 创建日期内容
            const lunarInfo = getFullLunarInfo(currentYear, selectedMonth, day);
            dayCell.innerHTML = `
              <div class="single-day-number">${day}</div>
              <div class="single-lunar-day">${lunarInfo.lunarDate}</div>
              ${holidayName ? `<div class="single-holiday-tag">${holidayName}</div>` : ''}
            `;

            // 添加完整农历信息到title属性（鼠标悬停显示）
            dayCell.title = `${currentYear}年${selectedMonth}月${day}日\n${lunarInfo.fullInfo}`;
          }

          singleMonthGrid.appendChild(dayCell);
        });
      });
    }
    
    // 初始化
    function init() {
      console.log('初始化年历系统');
      renderYearlyCalendar();
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
    } else {
      init();
    }
  </script>
</body>
</html>
