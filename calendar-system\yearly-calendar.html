<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>年历 - A4格式</title>
  <style>
    /* A4页面设置 */
    @page {
      size: A4;
      margin: 8mm;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', 'Segoe UI', sans-serif;
      font-size: 8px;
      line-height: 1.1;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      width: 210mm;
      height: 297mm;
      min-height: 100vh;
    }
    
    .year-header {
      text-align: center;
      margin-bottom: 20px;
      padding: 20px 0;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .year-title {
      font-size: 28px;
      font-weight: 700;
      background: linear-gradient(45deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .year-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 15px;
      margin-top: 10px;
    }

    .year-controls button {
      padding: 8px 16px;
      background: linear-gradient(45deg, #667eea, #764ba2);
      border: none;
      border-radius: 25px;
      cursor: pointer;
      font-size: 12px;
      color: white;
      font-weight: 500;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .year-controls button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .year-controls button:active {
      transform: translateY(0);
    }
    
    /* 年历网格布局 - 3列4行 */
    .yearly-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(4, 1fr);
      gap: 12px;
      height: 260mm;
      width: 194mm;
      margin: 0 auto;
      padding: 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    /* 每个月的容器 */
    .month-container {
      border: none;
      border-radius: 12px;
      overflow: hidden;
      background: rgba(255, 255, 255, 0.95);
      display: flex;
      flex-direction: column;
      height: 62mm;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .month-container:hover {
      transform: translateY(-5px) scale(1.02);
      box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
      border-color: rgba(102, 126, 234, 0.5);
    }

    .month-header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      padding: 6px 8px;
      text-align: center;
      font-weight: 600;
      font-size: 10px;
      color: white;
      border-bottom: none;
      height: 16px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
    
    .weekday-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: rgba(102, 126, 234, 0.1);
      font-size: 7px;
      font-weight: 600;
      height: 10px;
      color: #667eea;
    }

    .weekday-header div {
      padding: 2px 1px;
      text-align: center;
      border-right: 1px solid rgba(102, 126, 234, 0.1);
      line-height: 6px;
    }

    .weekday-header div:last-child {
      border-right: none;
    }

    .month-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      flex: 1;
    }

    .day-cell {
      border-right: 1px solid rgba(255, 255, 255, 0.3);
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      padding: 1px;
      font-size: 6px;
      position: relative;
      height: 8mm;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      transition: all 0.2s ease;
      background: rgba(255, 255, 255, 0.8);
    }

    .day-cell:nth-child(7n) {
      border-right: none;
    }

    .day-cell.empty {
      background: rgba(248, 249, 250, 0.5);
    }

    .day-cell.weekend {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      color: #1976d2;
    }

    .day-cell.holiday {
      background: linear-gradient(135deg, #fff3e0, #ffcc02);
    }

    .day-cell.solar-holiday {
      background: linear-gradient(135deg, #e8f5e8, #4caf50);
      color: #2e7d32;
    }

    .day-cell.lunar-holiday {
      background: linear-gradient(135deg, #fff8e1, #ffc107);
      color: #f57c00;
    }

    .day-cell.legal-holiday {
      background: linear-gradient(135deg, #ffebee, #f44336);
      color: #c62828;
      border: 1px solid #f44336;
      box-shadow: 0 2px 4px rgba(244, 67, 54, 0.2);
    }
    
    .day-number {
      font-weight: 700;
      color: #2c3e50;
      font-size: 8px;
      line-height: 1;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .lunar-day {
      font-size: 5px;
      color: #7b68ee;
      margin-top: 1px;
      line-height: 1;
      font-weight: 500;
    }

    .holiday-tag {
      font-size: 4px;
      color: white;
      text-align: center;
      position: absolute;
      bottom: 1px;
      left: 1px;
      right: 1px;
      line-height: 1;
      background: rgba(244, 67, 54, 0.8);
      border-radius: 2px;
      padding: 0.5px;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
    
    /* 打印样式 */
    @media print {
      body {
        font-size: 8px;
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 0;
        background: white !important;
      }

      .year-header {
        margin-bottom: 5mm;
        padding: 2mm 0;
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc;
      }

      .year-title {
        font-size: 16px;
        color: #333 !important;
        background: none !important;
        -webkit-text-fill-color: #333 !important;
      }

      .year-controls {
        display: none;
      }

      .yearly-grid {
        height: 260mm;
        width: 194mm;
        margin: 0 auto;
        gap: 3mm;
        background: white !important;
        padding: 10px;
      }

      .month-container {
        break-inside: avoid;
        height: 62mm;
        border: 1px solid #999;
        background: white !important;
        box-shadow: none !important;
      }

      .month-header {
        font-size: 8px;
        height: 12px;
        padding: 2px 4px;
        background: #f0f0f0 !important;
        color: #333 !important;
      }

      .weekday-header {
        font-size: 6px;
        height: 8px;
        background: #f8f8f8 !important;
        color: #666 !important;
      }

      .weekday-header div {
        line-height: 6px;
      }

      .day-cell {
        height: 7.5mm;
        font-size: 5px;
        background: white !important;
      }

      .day-cell.weekend {
        background: #f0f7ff !important;
      }

      .day-cell.legal-holiday {
        background: #ffe4e1 !important;
        border: 1px solid #ff6b6b !important;
      }

      .day-cell.solar-holiday {
        background: #f0f8ff !important;
      }

      .day-cell.lunar-holiday {
        background: #fff8dc !important;
      }

      .day-number {
        font-size: 6px;
        color: #333 !important;
      }

      .lunar-day {
        font-size: 3px;
        color: #666 !important;
      }

      .holiday-tag {
        font-size: 2.5px;
        background: #d9534f !important;
        color: white !important;
      }
    }
    
    /* 单月显示模式 */
    .single-month-mode {
      display: none;
      max-width: 900px;
      margin: 30px auto;
      padding: 30px;
    }

    .single-month-mode.active {
      display: block;
    }

    .single-month-container {
      border: none;
      border-radius: 20px;
      overflow: hidden;
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .single-month-header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 20px;
      text-align: center;
      font-size: 28px;
      font-weight: 700;
      display: flex;
      justify-content: space-between;
      align-items: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .back-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 10px 20px;
      border-radius: 25px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .back-button:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .header-spacer {
      width: 80px;
    }

    .single-weekday-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: #f8f9fa;
      font-size: 14px;
      font-weight: bold;
    }

    .single-weekday-header div {
      padding: 12px;
      text-align: center;
      border-right: 1px solid #ddd;
    }

    .single-weekday-header div:last-child {
      border-right: none;
    }

    .single-month-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
    }

    .single-day-cell {
      border-right: 1px solid rgba(255, 255, 255, 0.3);
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      padding: 15px 10px;
      font-size: 16px;
      position: relative;
      min-height: 90px;
      display: flex;
      flex-direction: column;
      align-items: center;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.8);
      cursor: pointer;
    }

    .single-day-cell:hover {
      background: rgba(102, 126, 234, 0.1);
      transform: scale(1.05);
    }

    .single-day-cell:nth-child(7n) {
      border-right: none;
    }

    .single-day-cell.empty {
      background: rgba(248, 249, 250, 0.5);
      cursor: default;
    }

    .single-day-cell.empty:hover {
      transform: none;
    }

    .single-day-cell.weekend {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      color: #1976d2;
    }

    .single-day-cell.holiday {
      background: linear-gradient(135deg, #fff3e0, #ffcc02);
    }

    .single-day-cell.solar-holiday {
      background: linear-gradient(135deg, #e8f5e8, #4caf50);
      color: #2e7d32;
    }

    .single-day-cell.lunar-holiday {
      background: linear-gradient(135deg, #fff8e1, #ffc107);
      color: #f57c00;
    }

    .single-day-cell.legal-holiday {
      background: linear-gradient(135deg, #ffebee, #f44336);
      color: #c62828;
      border: 2px solid #f44336;
      box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
    }

    .single-day-number {
      font-weight: 700;
      color: #2c3e50;
      font-size: 22px;
      margin-bottom: 6px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .single-lunar-day {
      font-size: 14px;
      color: #7b68ee;
      margin-bottom: 6px;
      font-weight: 500;
    }

    .single-holiday-tag {
      font-size: 12px;
      color: white;
      text-align: center;
      position: absolute;
      bottom: 6px;
      left: 6px;
      right: 6px;
      background: linear-gradient(45deg, #f44336, #e91e63);
      padding: 4px 6px;
      border-radius: 12px;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
    }

    /* 响应式调整 */
    @media (max-width: 1200px) {
      .yearly-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(6, 1fr);
      }
    }

    @media (max-width: 800px) {
      .yearly-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(12, 1fr);
      }

      .single-month-mode {
        margin: 10px;
        padding: 10px;
      }

      .single-day-cell {
        min-height: 60px;
        padding: 8px 4px;
      }

      .single-day-number {
        font-size: 16px;
      }

      .single-lunar-day {
        font-size: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="year-header">
    <h1 class="year-title" id="year-title">2025年日历</h1>
    <div class="year-controls">
      <button type="button" onclick="changeYear(-1)">上一年</button>
      <button type="button" onclick="window.print()">打印日历</button>
      <button type="button" onclick="changeYear(1)">下一年</button>
    </div>
  </div>
  
  <div class="yearly-grid" id="yearly-grid">
    <!-- 12个月的日历将通过JavaScript生成 -->
  </div>

  <!-- 单月显示模式 -->
  <div class="single-month-mode" id="single-month-mode">
    <div class="single-month-container">
      <div class="single-month-header">
        <button type="button" class="back-button" onclick="showYearlyView()">← 返回年历</button>
        <span id="single-month-title">2025年1月</span>
        <div class="header-spacer"></div> <!-- 占位符保持居中 -->
      </div>
      <div class="single-weekday-header">
        <div>日</div>
        <div>一</div>
        <div>二</div>
        <div>三</div>
        <div>四</div>
        <div>五</div>
        <div>六</div>
      </div>
      <div class="single-month-grid" id="single-month-grid">
        <!-- 单月日历内容将通过JavaScript生成 -->
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentYear = new Date().getFullYear();
    let currentViewMode = 'yearly'; // 'yearly' 或 'single'
    let selectedMonth = 1;
    
    // 公历节假日数据
    const solarHolidays = {
      "01-01": "元旦",
      "02-14": "情人节",
      "03-08": "妇女节",
      "03-12": "植树节",
      "03-15": "消费者权益日",
      "04-01": "愚人节",
      "04-07": "世界卫生日",
      "05-01": "劳动节",
      "05-04": "青年节",
      "06-01": "儿童节",
      "06-05": "世界环境日",
      "07-01": "建党节",
      "07-11": "世界人口日",
      "08-01": "建军节",
      "09-10": "教师节",
      "09-17": "国际和平日",
      "09-27": "世界旅游日",
      "10-01": "国庆节",
      "10-31": "万圣夜",
      "11-23": "感恩节",
      "12-01": "世界艾滋病日",
      "12-24": "平安夜",
      "12-25": "圣诞节"
    };

    // 农历节假日数据
    const lunarHolidays = {
      "正月初一": "春节",
      "正月初二": "春节",
      "正月初三": "春节",
      "正月十五": "元宵节",
      "二月初二": "龙抬头",
      "五月初五": "端午节",
      "六月廿四": "火把节",
      "七月初七": "七夕节",
      "七月十五": "中元节",
      "八月十五": "中秋节",
      "九月初九": "重阳节",
      "腊月初八": "腊八节",
      "腊月廿三": "小年",
      "腊月三十": "除夕"
    };

    // 2025年特殊节假日（国家法定假日）
    const specialHolidays2025 = {
      "2025-01-29": "春节",
      "2025-01-30": "春节",
      "2025-01-31": "春节",
      "2025-02-01": "春节",
      "2025-02-02": "春节",
      "2025-04-04": "清明节",
      "2025-04-05": "清明节",
      "2025-04-06": "清明节",
      "2025-05-01": "劳动节",
      "2025-05-02": "劳动节",
      "2025-05-03": "劳动节",
      "2025-06-02": "端午节",
      "2025-09-15": "中秋节",
      "2025-09-16": "中秋节",
      "2025-09-17": "中秋节",
      "2025-10-01": "国庆节",
      "2025-10-02": "国庆节",
      "2025-10-03": "国庆节",
      "2025-10-04": "国庆节",
      "2025-10-05": "国庆节",
      "2025-10-06": "国庆节",
      "2025-10-07": "国庆节"
    };
    
    // 月份名称
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 干支纪年相关数据
    const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    const zodiacAnimals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

    // 农历月份和日期名称
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];

    // 农历数字转中文
    const chineseNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
    const chineseDayNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
                              '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                              '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
    
    // 生成月历矩阵
    function generateMonthMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      const startDay = firstDay.getDay();
      
      const matrix = [];
      let currentWeek = [];
      
      // 添加月初的空白天
      for (let i = 0; i < startDay; i++) {
        currentWeek.push(null);
      }
      
      // 添加月份中的所有天
      for (let day = 1; day <= daysInMonth; day++) {
        currentWeek.push(day);
        
        if (currentWeek.length === 7) {
          matrix.push(currentWeek);
          currentWeek = [];
        }
      }
      
      // 添加月末的空白天
      if (currentWeek.length > 0) {
        while (currentWeek.length < 7) {
          currentWeek.push(null);
        }
        matrix.push(currentWeek);
      }
      
      return matrix;
    }
    
    // 计算干支纪年
    function getGanZhiYear(year) {
      // 以1984年（甲子年）为基准
      const baseYear = 1984;
      const yearOffset = year - baseYear;

      const heavenlyIndex = yearOffset % 10;
      const earthlyIndex = yearOffset % 12;

      const heavenly = heavenlyStems[heavenlyIndex < 0 ? heavenlyIndex + 10 : heavenlyIndex];
      const earthly = earthlyBranches[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];
      const zodiac = zodiacAnimals[earthlyIndex < 0 ? earthlyIndex + 12 : earthlyIndex];

      return { heavenly, earthly, zodiac, ganZhi: heavenly + earthly };
    }

    // 2025年农历数据（更准确的农历对应关系）
    const lunar2025Data = {
      // 格式：'MM-DD': { month: 农历月, day: 农历日, monthName: '月份名', dayName: '日期名' }
      '01-01': { month: 12, day: 2, monthName: '腊月', dayName: '初二' },
      '01-29': { month: 1, day: 1, monthName: '正月', dayName: '初一' }, // 春节
      '02-12': { month: 1, day: 15, monthName: '正月', dayName: '十五' }, // 元宵节
      '02-26': { month: 2, day: 1, monthName: '二月', dayName: '初一' },
      '02-28': { month: 2, day: 3, monthName: '二月', dayName: '初三' }, // 龙抬头
      '03-28': { month: 3, day: 1, monthName: '三月', dayName: '初一' },
      '04-26': { month: 4, day: 1, monthName: '四月', dayName: '初一' },
      '05-26': { month: 5, day: 1, monthName: '五月', dayName: '初一' },
      '05-30': { month: 5, day: 5, monthName: '五月', dayName: '初五' }, // 端午节
      '06-24': { month: 6, day: 1, monthName: '六月', dayName: '初一' },
      '07-17': { month: 6, day: 24, monthName: '六月', dayName: '廿四' }, // 火把节
      '07-23': { month: 7, day: 1, monthName: '七月', dayName: '初一' },
      '08-10': { month: 7, day: 7, monthName: '七月', dayName: '初七' }, // 七夕节
      '08-06': { month: 7, day: 15, monthName: '七月', dayName: '十五' }, // 中元节
      '08-22': { month: 8, day: 1, monthName: '八月', dayName: '初一' },
      '09-15': { month: 8, day: 15, monthName: '八月', dayName: '十五' }, // 中秋节
      '09-20': { month: 9, day: 1, monthName: '九月', dayName: '初一' },
      '10-13': { month: 9, day: 9, monthName: '九月', dayName: '初九' }, // 重阳节
      '10-20': { month: 10, day: 1, monthName: '十月', dayName: '初一' },
      '11-18': { month: 11, day: 1, monthName: '冬月', dayName: '初一' },
      '12-18': { month: 12, day: 1, monthName: '腊月', dayName: '初一' },
      '01-05': { month: 12, day: 8, monthName: '腊月', dayName: '初八' }, // 腊八节（次年1月）
      '01-22': { month: 12, day: 23, monthName: '腊月', dayName: '廿三' }, // 小年（次年1月）
      '01-28': { month: 12, day: 30, monthName: '腊月', dayName: '三十' }  // 除夕（次年1月）
    };

    // 改进的农历转换（基于实际农历数据）
    function getLunarDate(year, month, day) {
      if (year === 2025) {
        // 使用真实的2025年农历数据
        const dateKey = `${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        if (lunar2025Data[dateKey]) {
          return `${lunar2025Data[dateKey].monthName}${lunar2025Data[dateKey].dayName}`;
        }

        // 对于没有精确数据的日期，进行近似计算
        const date = new Date(year, month - 1, day);

        // 找到最近的农历月初
        let nearestNewMoon = null;
        let minDiff = Infinity;

        for (const [key, value] of Object.entries(lunar2025Data)) {
          if (value.day === 1) { // 农历月初
            const [m, d] = key.split('-').map(Number);
            const lunarNewMoonDate = new Date(year, m - 1, d);
            const diff = Math.abs(date - lunarNewMoonDate);
            if (diff < minDiff) {
              minDiff = diff;
              nearestNewMoon = { date: lunarNewMoonDate, lunar: value, key };
            }
          }
        }

        if (nearestNewMoon) {
          const daysDiff = Math.floor((date - nearestNewMoon.date) / (1000 * 60 * 60 * 24));
          const lunarDay = daysDiff + 1;

          if (lunarDay > 0 && lunarDay <= 30) {
            return `${nearestNewMoon.lunar.monthName}${lunarDays[lunarDay - 1]}`;
          }
        }
      }

      // 对于其他年份或无法计算的日期，使用简化算法
      const date = new Date(year, month - 1, day);
      const baseDate = new Date(year, 0, 1);
      const dayOfYear = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));

      const lunarMonthIndex = Math.floor(dayOfYear / 29.5) % 12;
      const lunarDayIndex = Math.floor(dayOfYear % 29.5);

      const lunarMonth = lunarMonths[lunarMonthIndex];
      const lunarDay = lunarDays[Math.min(lunarDayIndex, 29)];

      return `${lunarMonth}${lunarDay}`;
    }

    // 获取完整的农历信息（包含干支纪年）
    function getFullLunarInfo(year, month, day) {
      const ganZhi = getGanZhiYear(year);
      const lunarDate = getLunarDate(year, month, day);

      return {
        ganZhiYear: ganZhi.ganZhi + '年',
        zodiacYear: ganZhi.zodiac + '年',
        lunarDate: lunarDate,
        fullInfo: `${ganZhi.ganZhi}${ganZhi.zodiac}年 ${lunarDate}`
      };
    }

    // 获取农历日期显示（用于日历格子中）
    function getLunarDateDisplay(year, month, day) {
      return getLunarDate(year, month, day);
    }

    // 获取年份的干支信息（用于标题显示）
    function getYearGanZhiInfo(year) {
      const ganZhi = getGanZhiYear(year);
      return `${ganZhi.ganZhi}${ganZhi.zodiac}年`;
    }

    // 自定义农历节假日检查函数（类似VB中的GetCustomLunarHoliday）
    function getCustomLunarHoliday(lunarDateStr) {
      const customHolidays = {
        "正月初一": "春节",
        "正月初二": "春节",
        "正月初三": "春节",
        "正月十五": "元宵节",
        "二月初二": "龙抬头",
        "四月初五": "清明节",
        "五月初五": "端午节",
        "七月初七": "七夕节",
        "七月十五": "中元节",
        "八月十五": "中秋节",
        "九月初九": "重阳节",
        "腊月初八": "腊八节",
        "腊月廿三": "小年",
        "腊月廿四": "小年",
        "腊月三十": "除夕",
        "六月廿四": "火把节"
      };

      // 检查是否为特定农历节日
      if (customHolidays[lunarDateStr]) {
        return customHolidays[lunarDateStr];
      }

      // 如果不是特定节日，返回农历日期的最后两个字符（日期部分）
      return lunarDateStr.length >= 2 ? lunarDateStr.slice(-2) : lunarDateStr;
    }
    
    // 获取节假日信息（包含类型）
    function getHolidayInfo(year, month, day) {
      // 检查特殊节假日（国家法定假日）
      const fullDateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      if (specialHolidays2025[fullDateStr]) {
        return { name: specialHolidays2025[fullDateStr], type: 'legal' };
      }

      // 检查公历节假日
      const solarDateStr = `${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      if (solarHolidays[solarDateStr]) {
        return { name: solarHolidays[solarDateStr], type: 'solar' };
      }

      // 检查农历节假日
      const lunarDate = getLunarDate(year, month, day);
      const customHoliday = getCustomLunarHoliday(lunarDate);

      // 如果自定义农历节假日返回的不是日期本身，说明是节日
      if (customHoliday !== lunarDate.slice(-2) && customHoliday !== lunarDate) {
        return { name: customHoliday, type: 'lunar' };
      }

      return null;
    }

    // 检查是否为节假日（兼容旧接口）
    function isHoliday(year, month, day) {
      const holidayInfo = getHolidayInfo(year, month, day);
      return holidayInfo ? holidayInfo.name : null;
    }

    // 获取节假日名称（兼容旧接口）
    function getHolidayName(day) {
      return isHoliday(currentYear, selectedMonth || currentMonth, day) || '';
    }
    
    // 检查是否为周末
    function isWeekend(dayIndex) {
      return dayIndex === 0 || dayIndex === 6;
    }
    
    // 渲染单个月份
    function renderMonth(year, month) {
      const monthContainer = document.createElement('div');
      monthContainer.className = 'month-container';
      monthContainer.onclick = () => showSingleMonth(month);

      // 月份标题
      const monthHeader = document.createElement('div');
      monthHeader.className = 'month-header';
      monthHeader.textContent = `${year}年${monthNames[month - 1]}`;
      monthContainer.appendChild(monthHeader);
      
      // 星期标题
      const weekdayHeader = document.createElement('div');
      weekdayHeader.className = 'weekday-header';
      ['日', '一', '二', '三', '四', '五', '六'].forEach(day => {
        const dayDiv = document.createElement('div');
        dayDiv.textContent = day;
        weekdayHeader.appendChild(dayDiv);
      });
      monthContainer.appendChild(weekdayHeader);
      
      // 月份网格
      const monthGrid = document.createElement('div');
      monthGrid.className = 'month-grid';
      
      const matrix = generateMonthMatrix(year, month);
      
      matrix.forEach(week => {
        week.forEach((day, dayIndex) => {
          const dayCell = document.createElement('div');
          dayCell.className = 'day-cell';
          
          if (day === null) {
            dayCell.classList.add('empty');
          } else {
            const holidayInfo = getHolidayInfo(year, month, day);
            const isWeekendDay = isWeekend(dayIndex);

            if (isWeekendDay) {
              dayCell.classList.add('weekend');
            }

            if (holidayInfo) {
              dayCell.classList.add('holiday');
              dayCell.classList.add(`${holidayInfo.type}-holiday`);
            }

            dayCell.innerHTML = `
              <div class="day-number">${day}</div>
              <div class="lunar-day">${getLunarDateDisplay(year, month, day)}</div>
              ${holidayInfo ? `<div class="holiday-tag">${holidayInfo.name}</div>` : ''}
            `;
          }
          
          monthGrid.appendChild(dayCell);
        });
      });
      
      monthContainer.appendChild(monthGrid);
      return monthContainer;
    }
    
    // 渲染整年日历
    function renderYearlyCalendar() {
      // 更新标题，包含干支纪年
      const ganZhiInfo = getYearGanZhiInfo(currentYear);
      document.getElementById('year-title').textContent = `${currentYear}年（${ganZhiInfo}）日历`;

      // 清空网格
      const yearlyGrid = document.getElementById('yearly-grid');
      yearlyGrid.innerHTML = '';

      // 生成12个月
      for (let month = 1; month <= 12; month++) {
        const monthElement = renderMonth(currentYear, month);
        yearlyGrid.appendChild(monthElement);
      }
    }
    
    // 改变年份
    function changeYear(delta) {
      currentYear += delta;
      if (currentViewMode === 'yearly') {
        renderYearlyCalendar();
      } else {
        renderSingleMonth();
      }
    }

    // 显示单个月份
    function showSingleMonth(month) {
      selectedMonth = month;
      currentViewMode = 'single';

      // 隐藏年历，显示单月
      document.getElementById('yearly-grid').style.display = 'none';
      document.getElementById('single-month-mode').classList.add('active');

      renderSingleMonth();
    }

    // 显示年历视图
    function showYearlyView() {
      currentViewMode = 'yearly';

      // 显示年历，隐藏单月
      document.getElementById('yearly-grid').style.display = 'grid';
      document.getElementById('single-month-mode').classList.remove('active');
    }

    // 渲染单月详细视图
    function renderSingleMonth() {
      // 更新标题，包含干支纪年
      const ganZhiInfo = getYearGanZhiInfo(currentYear);
      document.getElementById('single-month-title').textContent = `${currentYear}年（${ganZhiInfo}）${monthNames[selectedMonth - 1]}`;

      // 生成日历矩阵
      const matrix = generateMonthMatrix(currentYear, selectedMonth);

      // 获取单月网格容器
      const singleMonthGrid = document.getElementById('single-month-grid');
      singleMonthGrid.innerHTML = '';

      // 渲染每一周
      matrix.forEach(week => {
        week.forEach((day, dayIndex) => {
          const dayCell = document.createElement('div');
          dayCell.className = 'single-day-cell';

          if (day === null) {
            // 空白天
            dayCell.classList.add('empty');
          } else {
            // 有效天
            const holidayInfo = getHolidayInfo(currentYear, selectedMonth, day);
            const isWeekendDay = isWeekend(dayIndex);

            if (isWeekendDay) {
              dayCell.classList.add('weekend');
            }

            if (holidayInfo) {
              dayCell.classList.add('holiday');
              dayCell.classList.add(`${holidayInfo.type}-holiday`);
            }

            // 创建日期内容
            const lunarInfo = getFullLunarInfo(currentYear, selectedMonth, day);
            dayCell.innerHTML = `
              <div class="single-day-number">${day}</div>
              <div class="single-lunar-day">${lunarInfo.lunarDate}</div>
              ${holidayInfo ? `<div class="single-holiday-tag">${holidayInfo.name}</div>` : ''}
            `;

            // 添加完整农历信息到title属性（鼠标悬停显示）
            dayCell.title = `${currentYear}年${selectedMonth}月${day}日\n${lunarInfo.fullInfo}`;
          }

          singleMonthGrid.appendChild(dayCell);
        });
      });
    }
    
    // 初始化
    function init() {
      console.log('初始化年历系统');
      renderYearlyCalendar();
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
    } else {
      init();
    }
  </script>
</body>
</html>
