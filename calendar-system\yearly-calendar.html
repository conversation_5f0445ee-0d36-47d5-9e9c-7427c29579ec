<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>年历 - A4格式</title>
  <style>
    /* A4页面设置 */
    @page {
      size: A4;
      margin: 10mm;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      font-size: 10px;
      line-height: 1.2;
      background: white;
    }
    
    .year-header {
      text-align: center;
      margin-bottom: 15px;
      padding: 10px 0;
    }
    
    .year-title {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }
    
    .year-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 15px;
      margin-top: 10px;
    }
    
    .year-controls button {
      padding: 5px 10px;
      background: #f0f0f0;
      border: 1px solid #ccc;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .year-controls button:hover {
      background: #e0e0e0;
    }
    
    /* 年历网格布局 - 3列4行 */
    .yearly-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(4, 1fr);
      gap: 8px;
      height: calc(100vh - 120px);
      max-height: 250mm;
    }
    
    /* 每个月的容器 */
    .month-container {
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow: hidden;
      background: white;
      display: flex;
      flex-direction: column;
    }
    
    .month-header {
      background: #f8f9fa;
      padding: 4px 8px;
      text-align: center;
      font-weight: bold;
      font-size: 11px;
      color: #333;
      border-bottom: 1px solid #ddd;
    }
    
    .weekday-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: #f0f0f0;
      font-size: 8px;
      font-weight: bold;
    }
    
    .weekday-header div {
      padding: 2px;
      text-align: center;
      border-right: 1px solid #ddd;
    }
    
    .weekday-header div:last-child {
      border-right: none;
    }
    
    .month-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      flex: 1;
    }
    
    .day-cell {
      border-right: 1px solid #eee;
      border-bottom: 1px solid #eee;
      padding: 1px;
      font-size: 8px;
      position: relative;
      min-height: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .day-cell:nth-child(7n) {
      border-right: none;
    }
    
    .day-cell.empty {
      background: #f9f9f9;
    }
    
    .day-cell.weekend {
      background: #f0f7ff;
    }
    
    .day-cell.holiday {
      background: #fff0f0;
    }
    
    .day-number {
      font-weight: bold;
      color: #333;
      font-size: 9px;
    }
    
    .lunar-day {
      font-size: 6px;
      color: #666;
      margin-top: 1px;
    }
    
    .holiday-tag {
      font-size: 5px;
      color: #d9534f;
      text-align: center;
      position: absolute;
      bottom: 1px;
      left: 0;
      right: 0;
    }
    
    /* 打印样式 */
    @media print {
      body {
        font-size: 9px;
      }
      
      .year-controls {
        display: none;
      }
      
      .yearly-grid {
        height: auto;
        max-height: none;
      }
      
      .month-container {
        break-inside: avoid;
      }
      
      .day-cell {
        min-height: 18px;
      }
    }
    
    /* 响应式调整 */
    @media (max-width: 1200px) {
      .yearly-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(6, 1fr);
      }
    }
    
    @media (max-width: 800px) {
      .yearly-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(12, 1fr);
      }
    }
  </style>
</head>
<body>
  <div class="year-header">
    <h1 class="year-title" id="year-title">2025年日历</h1>
    <div class="year-controls">
      <button onclick="changeYear(-1)">上一年</button>
      <button onclick="window.print()">打印日历</button>
      <button onclick="changeYear(1)">下一年</button>
    </div>
  </div>
  
  <div class="yearly-grid" id="yearly-grid">
    <!-- 12个月的日历将通过JavaScript生成 -->
  </div>

  <script>
    // 全局变量
    let currentYear = new Date().getFullYear();
    
    // 节假日数据
    const holidays = {
      "2025-01-01": "元旦",
      "2025-01-29": "春节",
      "2025-01-30": "春节",
      "2025-02-01": "春节",
      "2025-02-02": "春节",
      "2025-02-03": "春节",
      "2025-04-04": "清明节",
      "2025-05-01": "劳动节",
      "2025-10-01": "国庆节",
      "2025-10-02": "国庆节",
      "2025-10-03": "国庆节",
      "2025-10-04": "国庆节",
      "2025-10-05": "国庆节"
    };
    
    // 月份名称
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 农历月份和日期名称
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
    
    // 生成月历矩阵
    function generateMonthMatrix(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const daysInMonth = new Date(year, month, 0).getDate();
      const startDay = firstDay.getDay();
      
      const matrix = [];
      let currentWeek = [];
      
      // 添加月初的空白天
      for (let i = 0; i < startDay; i++) {
        currentWeek.push(null);
      }
      
      // 添加月份中的所有天
      for (let day = 1; day <= daysInMonth; day++) {
        currentWeek.push(day);
        
        if (currentWeek.length === 7) {
          matrix.push(currentWeek);
          currentWeek = [];
        }
      }
      
      // 添加月末的空白天
      if (currentWeek.length > 0) {
        while (currentWeek.length < 7) {
          currentWeek.push(null);
        }
        matrix.push(currentWeek);
      }
      
      return matrix;
    }
    
    // 获取农历日期（简化版）
    function getLunarDate(year, month, day) {
      const dayOfYear = Math.floor((new Date(year, month - 1, day) - new Date(year, 0, 1)) / (1000 * 60 * 60 * 24));
      const lunarMonth = Math.floor(dayOfYear / 30) % 12;
      const lunarDay = (dayOfYear % 30);
      return `${lunarMonths[lunarMonth]}${lunarDays[lunarDay]}`;
    }
    
    // 检查是否为节假日
    function isHoliday(year, month, day) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return holidays[dateStr];
    }
    
    // 检查是否为周末
    function isWeekend(dayIndex) {
      return dayIndex === 0 || dayIndex === 6;
    }
    
    // 渲染单个月份
    function renderMonth(year, month) {
      const monthContainer = document.createElement('div');
      monthContainer.className = 'month-container';
      
      // 月份标题
      const monthHeader = document.createElement('div');
      monthHeader.className = 'month-header';
      monthHeader.textContent = `${year}年${monthNames[month - 1]}`;
      monthContainer.appendChild(monthHeader);
      
      // 星期标题
      const weekdayHeader = document.createElement('div');
      weekdayHeader.className = 'weekday-header';
      ['日', '一', '二', '三', '四', '五', '六'].forEach(day => {
        const dayDiv = document.createElement('div');
        dayDiv.textContent = day;
        weekdayHeader.appendChild(dayDiv);
      });
      monthContainer.appendChild(weekdayHeader);
      
      // 月份网格
      const monthGrid = document.createElement('div');
      monthGrid.className = 'month-grid';
      
      const matrix = generateMonthMatrix(year, month);
      
      matrix.forEach(week => {
        week.forEach((day, dayIndex) => {
          const dayCell = document.createElement('div');
          dayCell.className = 'day-cell';
          
          if (day === null) {
            dayCell.classList.add('empty');
          } else {
            const holidayName = isHoliday(year, month, day);
            const isWeekendDay = isWeekend(dayIndex);
            
            if (isWeekendDay) {
              dayCell.classList.add('weekend');
            }
            
            if (holidayName) {
              dayCell.classList.add('holiday');
            }
            
            dayCell.innerHTML = `
              <div class="day-number">${day}</div>
              <div class="lunar-day">${getLunarDate(year, month, day)}</div>
              ${holidayName ? `<div class="holiday-tag">${holidayName}</div>` : ''}
            `;
          }
          
          monthGrid.appendChild(dayCell);
        });
      });
      
      monthContainer.appendChild(monthGrid);
      return monthContainer;
    }
    
    // 渲染整年日历
    function renderYearlyCalendar() {
      // 更新标题
      document.getElementById('year-title').textContent = `${currentYear}年日历`;
      
      // 清空网格
      const yearlyGrid = document.getElementById('yearly-grid');
      yearlyGrid.innerHTML = '';
      
      // 生成12个月
      for (let month = 1; month <= 12; month++) {
        const monthElement = renderMonth(currentYear, month);
        yearlyGrid.appendChild(monthElement);
      }
    }
    
    // 改变年份
    function changeYear(delta) {
      currentYear += delta;
      renderYearlyCalendar();
    }
    
    // 初始化
    function init() {
      console.log('初始化年历系统');
      renderYearlyCalendar();
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
    } else {
      init();
    }
  </script>
</body>
</html>
