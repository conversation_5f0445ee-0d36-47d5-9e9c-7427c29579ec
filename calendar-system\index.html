<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>日历系统</title>
  <link rel="stylesheet" href="./style.css">
</head>
<body>
  <div id="app">
    <div class="calendar-container">
      <div class="calendar-header">
        <button type="button" onclick="prevYear()">上一年</button>
        <button type="button" onclick="prevMonth()">上一月</button>
        <h2 id="current-date">2025年7月</h2>
        <button type="button" onclick="nextMonth()">下一月</button>
        <button type="button" onclick="nextYear()">下一年</button>
      </div>
      <div class="calendar-grid">
        <div class="weekday-header">
          <div>日</div>
          <div>一</div>
          <div>二</div>
          <div>三</div>
          <div>四</div>
          <div>五</div>
          <div>六</div>
        </div>
        <div id="calendar-body">
          <!-- 日历内容将通过JavaScript生成 -->
        </div>
      </div>
      <div class="calendar-footer">
        <button type="button" onclick="printCalendar()">打印日历</button>
        <button type="button" onclick="exportToPdf()">导出PDF</button>
      </div>
    </div>
  </div>

  <script src="./app.js"></script>
</body>
</html>