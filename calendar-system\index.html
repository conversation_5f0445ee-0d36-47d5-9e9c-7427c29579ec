<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>日历系统</title>
  <!-- 引入Vue 3 -->
  <script src="https://unpkg.com/vue@next"></script>
  <!-- 引入lunarjs -->
  <script src="https://cdn.jsdelivr.net/npm/lunar-calendar/lunar-calendar.min.js"></script>
  <!-- 引入print-js -->
  <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
  <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css">
  <!-- 引入自定义样式 -->
  <link rel="stylesheet" href="./style.css">
</head>
<body>
  <div id="app">
    <div class="calendar-container">
      <div class="calendar-header">
        <button @click="prevYear">上一年</button>
        <button @click="prevMonth">上一月</button>
        <h2>{{ currentYear }}年{{ currentMonth }}月</h2>
        <button @click="nextMonth">下一月</button>
        <button @click="nextYear">下一年</button>
      </div>
      <div class="calendar-grid">
        <div class="weekday-header">
          <div>日</div>
          <div>一</div>
          <div>二</div>
          <div>三</div>
          <div>四</div>
          <div>五</div>
          <div>六</div>
        </div>
        <div v-for="(week, weekIndex) in monthMatrix" :key="weekIndex" class="week-row">
          <div v-for="(day, dayIndex) in week" :key="dayIndex" class="day-cell"
               :class="{ 'empty': !day, 'weekend': isWeekend(weekIndex, dayIndex), 'holiday': isHoliday(day) }">
            <div v-if="day" class="day-number">{{ day }}</div>
            <div v-if="day" class="lunar-day">{{ getLunarDay(currentYear, currentMonth, day) }}</div>
            <div v-if="day && isHoliday(day)" class="holiday-tag">{{ getHolidayName(day) }}</div>
          </div>
        </div>
      </div>
      <div class="calendar-footer">
        <button @click="printCalendar">打印日历</button>
        <button @click="exportToPdf">导出PDF</button>
      </div>
    </div>
  </div>

  <!-- 引入App脚本 -->
  <script src="./app.js"></script>
</body>
</html>